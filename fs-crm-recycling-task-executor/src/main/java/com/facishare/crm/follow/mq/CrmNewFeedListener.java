package com.facishare.crm.follow.mq;

import com.alibaba.fastjson.JSON;
import org.apache.rocketmq.common.message.MessageExt;
import com.facishare.crm.follow.common.FollowRateLimiterService;
import com.facishare.crm.follow.enums.ActionCodeEnum;
import com.facishare.crm.follow.enums.ApiNameEnum;
import com.facishare.crm.follow.enums.FeedTypeEnums;
import com.facishare.crm.follow.enums.OperateTypeEnums;
import com.facishare.crm.follow.model.CrmNewFeedMessage;
import com.facishare.crm.follow.model.UpdateFollowDealModel;
import com.facishare.crm.follow.service.CommonService;
import com.facishare.crm.follow.service.CustomerFollowService;
import com.facishare.crm.follow.util.CommonUtils;
import com.facishare.crm.follow.util.ConstantUtils;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.recycling.task.executor.util.GrayUtils;
import com.facishare.crm.recycling.task.executor.util.SearchUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.mq.RocketMQMessageListener;
import com.facishare.paas.appframework.common.service.CRMNotificationServiceImpl;
import com.facishare.paas.appframework.common.service.dto.InternationalItem;
import com.facishare.paas.appframework.common.service.model.NewCrmNotification;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.appframework.metadata.DescribeLogicService;
import com.facishare.paas.appframework.metadata.MetaDataService;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.action.ActionContext;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.service.IObjectDataService;
import com.facishare.paas.metadata.dao.pg.mapper.metadata.SpecialTableMapper;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.describe.ObjectReferenceFieldDescribe;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

import static com.facishare.crm.follow.util.CommonUtils.buildContext;
import static com.facishare.crm.follow.util.CommonUtils.getOwner;
import static com.facishare.crm.follow.util.ConstantUtils.ACCOUNT_OBJ;
import static com.facishare.crm.follow.util.ConstantUtils.LIFE_STATUS;
import static com.facishare.crm.follow.util.ConstantUtils.*;
import static com.facishare.crm.recycling.task.executor.util.ConstantUtils.BIZ_STATUS;
import static com.facishare.crm.recycling.task.executor.util.ConstantUtils.LAST_FOLLOW_TIME;
import static com.facishare.crm.recycling.task.executor.util.ConstantUtils.LEADS_OBJ;
import static com.facishare.crm.recycling.task.executor.util.ObjectDataUtils.buildUser;

/**
 * @Description
 * <AUTHOR>
 * @Date 2020/5/21 14:42
 */
@Slf4j
public class CrmNewFeedListener implements RocketMQMessageListener {

    @Autowired
    private CustomerFollowService customerFollowService;

    @Autowired
    private IObjectDataService objectDataPgService;

    @Autowired
    private CommonService commonService;

    @Autowired
    private FollowRateLimiterService followRateLimiterService;

    @Autowired
    protected SpecialTableMapper specialTableMapper;

    @Autowired
    private MetaDataService metaDataService;

    @Autowired
    @Qualifier("describeLogicService")
    private DescribeLogicService describeService;

    @Autowired
    private GrayUtils grayUtils;


    private CRMNotificationServiceImpl crmNotificationService = (CRMNotificationServiceImpl) SpringUtil.getContext()
            .getBean("crmNotificationService");


    @Override
    public void consumeMessage(List<MessageExt> list) {
        if (CollectionUtils.empty(list)) {
            return;
        }

        for (MessageExt message : list) {
            String body = new String(message.getBody(), StandardCharsets.UTF_8);
            CrmNewFeedMessage messageObj = JSON.parseObject(body, CrmNewFeedMessage.class);
            if (messageObj.getOperationType() == null || messageObj.getOperationType() != 1) {
                log.info("CrmNewFeedListener return msgId:{},messageObj:{}", message.getMsgId(), messageObj);
                continue;
            }
            log.info("CrmNewFeedListener msgId:{},messageObj:{}", message.getMsgId(), messageObj);
            consumeMessage(messageObj);
        }

    }


    private void consumeMessage(CrmNewFeedMessage crmFeedMQMessage) {
        if (CollectionUtils.empty(crmFeedMQMessage.getCrmObjects())) {
            return;
        }

        if (GrayUtils.skipFeedTenantId(String.valueOf(crmFeedMQMessage.getTenantId()))){
            return;
        }
        followRateLimiterService.getCrmFeedRateLimiter().acquire();
        boolean isCombine = OperateTypeEnums.COMBINE.getValue().equals(crmFeedMQMessage.getFeedType());
        //增加商机最后跟进时间
        updateOpportunityObjFollowTime(crmFeedMQMessage);
        if (!isCombine) {
            sendNotification(crmFeedMQMessage);
        }

        String tenantId = String.valueOf(crmFeedMQMessage.getTenantId());
        String operatorId = String.valueOf(crmFeedMQMessage.getSenderId());
        UpdateFollowDealModel updateFollowDealModel = UpdateFollowDealModel.builder().tenantId(tenantId)
                .follower(operatorId).build();
        List<UpdateFollowDealModel.Content> contents = new ArrayList<>();
        List<UpdateFollowDealModel.Content> leadsContents = new ArrayList<>();

        List<String> apiNameList = new ArrayList<>();
        crmFeedMQMessage.getCrmObjects().stream().forEach(y -> apiNameList.add(y.getApiName()));

        Map<String, List<String>> mapSettings = commonService.getFollowSetting(tenantId, apiNameList, ActionCodeEnum.ADD_EVENT);
        // 无线索的跟进配置，去判断是否更新线索为跟进中
        if (CollectionUtils.empty(mapSettings) || CollectionUtils.empty(mapSettings.get(LEADS_OBJ))) {
            log.info("objectFollowDealSettingType is empty,tenantId:{},apiNames:{},actionCode:{}", tenantId, apiNameList, ActionCodeEnum.ADD_EVENT.getActionCode());
            updateLeadsStatus(crmFeedMQMessage);
        }
        if (CollectionUtils.empty(mapSettings)) {
            return;
        }
        for (CrmNewFeedMessage.CrmObject crmObject : crmFeedMQMessage.getCrmObjects()) {
            String apiName = crmObject.getApiName();
            String dataId = crmObject.getDataId();
            // 配置客户跟进行为的所有对象
            List<String> accountApiNames = mapSettings.get(ACCOUNT_OBJ);
            // 配置线索跟进行为的所有对象
            List<String> leadsApiNames = mapSettings.get(LEADS_OBJ);

            if ("VisitingObj".equals(crmObject.getApiName())) {
                continue;
            }

            if (ACCOUNT_OBJ.equals(apiName) && accountApiNames != null && accountApiNames.contains(ACCOUNT_OBJ)) {
                addContents(crmFeedMQMessage, operatorId, contents, dataId, ACCOUNT_OBJ);
            } else if (LEADS_OBJ.equals(apiName) && leadsApiNames != null && leadsApiNames.contains(LEADS_OBJ)) {
                addContents(crmFeedMQMessage, operatorId, leadsContents, dataId, LEADS_OBJ);
            } else {
                String customerId;
                IObjectData objectData;
                try {
                    User user = new User(tenantId, User.SUPPER_ADMIN_USER_ID);
                    objectData = objectDataPgService.findById(dataId, tenantId, buildContext(user), apiName);
                    if (objectData == null) {
                        log.warn("objectData is null,apiName:{} ,message:{}", apiName, crmFeedMQMessage);
                        continue;
                    }
                    List<ObjectReferenceFieldDescribe> accountObjs = commonService.objectReferenceFieldDescribe(tenantId, dataId, apiName);
                    for (ObjectReferenceFieldDescribe accountObj : accountObjs) {
                        if (objectData.get(accountObj.getApiName()) == null || ("").equals(objectData.get(accountObj.getApiName()))) {
                            log.info("accountObj.getApiName is null:{},tenantId:{},apiName:{}", dataId, tenantId, apiName);
                            continue;
                        }
                        customerId = objectData.get(accountObj.getApiName()).toString();
                        // 客户跟进包含该对象，并且是查找关联客户
                        if (accountApiNames != null && accountApiNames.contains(accountObj.getDescribeApiName()) && ACCOUNT_OBJ.equals(accountObj.getTargetApiName())) {
                            addContents(crmFeedMQMessage, operatorId, contents, customerId, ACCOUNT_OBJ);
                        } else if (leadsApiNames != null && leadsApiNames.contains(accountObj.getDescribeApiName()) && LEADS_OBJ.equals(accountObj.getTargetApiName())) {
                            addContents(crmFeedMQMessage, operatorId, leadsContents, customerId, LEADS_OBJ);
                        }
                    }
                } catch (MetadataServiceException e) {
                    log.error(e.getMessage(), e);
                    throw new RuntimeException(e);
                }
            }

        }

        updateFollowDealModel.setContents(contents);
        customerFollowService.updateFollowDealTime(updateFollowDealModel);

        updateFollowDealModel.setContents(updateLeadsStatus(leadsContents, tenantId));
        customerFollowService.updateFollowDealTime(updateFollowDealModel);

    }

    private void addContents(CrmNewFeedMessage crmFeedMQMessage, String operatorId, List<UpdateFollowDealModel.Content> contents, String objectId, String apiName) {
        Long createTime = crmFeedMQMessage.getCreateTime();
        String eventId = crmFeedMQMessage.getEventId();
        if (StringUtils.isBlank(objectId)) {
            return;
        }
        for (UpdateFollowDealModel.Content content : contents) {
            if (objectId.equals(content.getObjectId()) && apiName.equals(content.getApiName())) {
                return;
            }
        }
        UpdateFollowDealModel.Content content = UpdateFollowDealModel.Content.builder()
                .objectId(objectId)
                .apiName(apiName)
                .lastFollowTime(createTime)
                .lastFollower(operatorId)
                .eventId(eventId)
                .build();
        contents.add(content);
    }

    private void updateOpportunityObjFollowTime(CrmNewFeedMessage crmFeedMQMessage) {
        List<UpdateFollowDealModel.Content> contents = new ArrayList<>();
        List<UpdateFollowDealModel.Content> newContents = new ArrayList<>();
        Long createTime = crmFeedMQMessage.getCreateTime();
        String tenantId = String.valueOf(crmFeedMQMessage.getTenantId());
        UpdateFollowDealModel updateFollowDealModel = UpdateFollowDealModel.builder().tenantId(tenantId).build();
        UpdateFollowDealModel.UpdateFollowDealModelBuilder builder2 = UpdateFollowDealModel.builder().tenantId(tenantId);
        if (GrayUtils.isUpdateNewOpportunityLastModified(tenantId)) {
            builder2.lastModifiedBy(String.valueOf(crmFeedMQMessage.getSenderId()));
        }
        UpdateFollowDealModel newUpdateFollowDealModel = builder2.build();
        List<CrmNewFeedMessage.CrmObject> crmObjects = crmFeedMQMessage.getCrmObjects();
        for (CrmNewFeedMessage.CrmObject crmObject : crmObjects) {
            if (Utils.OPPORTUNITY_API_NAME.equals(crmObject.getApiName())) {
                UpdateFollowDealModel.Content content = UpdateFollowDealModel.Content.builder()
                        .objectId(crmObject.getDataId())
                        .apiName(Utils.OPPORTUNITY_API_NAME)
                        .lastFollowTime(createTime)
                        .eventId(crmFeedMQMessage.getEventId())
                        .build();
                contents.add(content);
            } else if (Utils.NEW_OPPORTUNITY_API_NAME.equals(crmObject.getApiName())) {
                UpdateFollowDealModel.Content content = UpdateFollowDealModel.Content.builder()
                        .objectId(crmObject.getDataId())
                        .apiName(Utils.NEW_OPPORTUNITY_API_NAME)
                        .sourceApiName("ActiveRecordObj")
                        .lastFollowTime(createTime)
                        .eventId(crmFeedMQMessage.getEventId())
                        .build();
                newContents.add(content);
            }
        }
        // 老商机
        updateFollowDealModel.setContents(contents);
        customerFollowService.updateFollowDealTime(updateFollowDealModel);
        // 新商机
        if (CollectionUtils.notEmpty(newContents)) {
            newUpdateFollowDealModel.setContents(newContents);
            customerFollowService.updateFollowDealTime(newUpdateFollowDealModel);
        }
    }

    /**
     * 发送提醒
     *
     * @param crmFeedMQMessage
     */
    private void sendNotification(CrmNewFeedMessage crmFeedMQMessage) {
        String tenantId = String.valueOf(crmFeedMQMessage.getTenantId());
        List<CrmNewFeedMessage.CrmObject> crmObjects = crmFeedMQMessage.getCrmObjects();
        for (CrmNewFeedMessage.CrmObject crmObject : crmObjects) {
            String apiName = crmObject.getApiName();
            String dataId = crmObject.getDataId();
            String operatorId = String.valueOf(crmFeedMQMessage.getSenderId());
            if (Utils.ACCOUNT_API_NAME.equals(apiName)) {
                sendNotification(String.valueOf(crmFeedMQMessage.getFeedType()), dataId, tenantId,
                        operatorId);
            }
        }
    }

    private void sendNotification(String feedType, String dataId, String tenantId, String userId) {
        //-10000系统发送的销售记录不需要通知
        if (Strings.isNullOrEmpty(dataId)
                || Strings.isNullOrEmpty(userId)
                || Integer.parseInt(userId) < 0) {
            return;
        }
        String title = "";
        String titleKey = "";
        if (FeedTypeEnums.EVENT.getValue().equals(feedType)) {
            title = I18N.text("sfa.CustomerBusiness.2513.3");
            titleKey = "sfa.CustomerBusiness.2513.3";
        } else if (FeedTypeEnums.SERVICE.getValue().equals(feedType)) {
            title = I18N.text("sfa.CustomerBusiness.2528.1");
            titleKey = "sfa.CustomerBusiness.2528.1";
        } else {
            return;
        }

        try {
            User user = new User(tenantId, userId);
            IObjectData objectData = objectDataPgService.findById(dataId, tenantId, buildContext(user), ACCOUNT_OBJ);
            if (objectData == null) {
                return;
            }
            if (CollectionUtils.notEmpty(objectData.getOwner())) {
                List<String> owners = objectData.getOwner();
                owners.remove(userId);
                Set<Integer> receiverIds = Sets.newHashSet();
                for (String owner : owners) {
                    receiverIds.add(Integer.parseInt(owner));
                }
                if (CollectionUtils.notEmpty(receiverIds)) {
                    Map<String, String> urlParameters = Maps.newHashMap();
                    urlParameters.put("objectApiName", Utils.ACCOUNT_API_NAME);
                    urlParameters.put("objectId", dataId);
                    crmNotificationService.sendNewCrmNotification(user, NewCrmNotification.builder()
                            .senderId(user.getUserId())
                            .type(19)
                            .title(title)
                            .fullContent(objectData.getName())
                            .titleInfo(InternationalItem.builder().internationalKey(titleKey).build())
                            .urlParameter(urlParameters)
                            .objectId(dataId)
                            .objectApiName(ACCOUNT_OBJ)
                            .receiverIDs(receiverIds)
                            .urlType(1)
                            .build());
                }
            }
        } catch (MetadataServiceException e) {
            log.error(e.getMessage(), e);
        }
    }


    /**
     * 更新线索为跟进中
     *
     * @param message
     */
    private void updateLeadsStatus(CrmNewFeedMessage message) {
        String tenantId = String.valueOf(message.getTenantId());
        for (CrmNewFeedMessage.CrmObject crmObject : message.getCrmObjects()) {
            if (LEADS_OBJ.equals(crmObject.getApiName())) {
                updateLeadsStatus(tenantId, crmObject.getDataId(), String.valueOf(message.getSenderId()));
            }
        }
    }

    /**
     * 不会查询跟进配置，待处理 的线索会变更为 跟进中.
     *
     * @param tenantId
     * @param leadsId
     */
    private void updateLeadsStatus(String tenantId, String leadsId, String userId) {
        try {
            IObjectData objectData = objectDataPgService.findById(leadsId, tenantId, ApiNameEnum.LEADS_OBJ.getApiName());
            if (objectData == null) {
                return;
            }
            if (ConstantUtils.NORMAL.equals(objectData.get(LIFE_STATUS))
                    && UN_PROCESSED.equals(objectData.get(BIZ_STATUS))) {
                String updateSql = String.format("UPDATE biz_leads SET biz_status='%s',leads_status='4',is_overtime = false " +
                                " WHERE tenant_id='%s'" +
                                " AND id='%s' AND biz_status='%s'", PROCESSED
                        , tenantId, leadsId, UN_PROCESSED);
                log.info("update leads status processed sql {}", updateSql);
                try {
                    customerFollowService.updateLeadsStatus(tenantId, leadsId, null, null);
                } catch (Exception e) {
                    log.error("update leads status error {}", tenantId, e);
                }
                objectData.set("biz_status", PROCESSED);
                if (StringUtils.isBlank(userId)) {
                    userId = "-10000";
                }
                addFlowRecord(tenantId, userId, Lists.newArrayList(objectData));
            } else {
                customerFollowService.updateLeadsOverTime(tenantId, leadsId);
            }
        } catch (Exception e) {
            log.error("updateLeadsStatus error :{},{}", tenantId, leadsId, e);
            throw new RuntimeException(e);
        }
    }

    /**
     * 【待处理】状态的线索更新为跟进中，更新跟进时间和跟进人
     * 状态不是【待处理】的线索 返回 统一处理 只更新跟进时间
     *
     * @param leadsContents
     * @param tenantId
     * @return
     */
    private List<UpdateFollowDealModel.Content> updateLeadsStatus(List<UpdateFollowDealModel.Content> leadsContents, String tenantId) {
        List<IObjectData> datas = new ArrayList<>();
        List<String> ids = Lists.newArrayList();
        for (UpdateFollowDealModel.Content content : leadsContents) {
            if (StringUtils.isBlank(content.getObjectId())) {
                continue;
            }
            IObjectData data = new ObjectData();
            data.setTenantId(tenantId);
            data.setDescribeApiName(content.getApiName());
            data.setId(content.getObjectId());
            data.set(LAST_FOLLOW_TIME, content.getLastFollowTime());
            data.set(LAST_FOLLOWER, content.getLastFollower());
            ids.add(content.getObjectId());
            datas.add(data);
        }

        if (CollectionUtils.empty(ids)) {
            return leadsContents;
        }
        List<IObjectData> byIds = null;
        try {
            byIds = objectDataPgService.findByIds(ids, tenantId, LEADS_OBJ, CommonUtils.buildContextAllInvalid(buildUser(tenantId)));

            List<String> unProcessedIds = byIds.stream().filter(x -> UN_PROCESSED.equals(x.get(BIZ_STATUS))).map(IObjectData::getId).collect(Collectors.toList());
            if (CollectionUtils.notEmpty(unProcessedIds)) {
                datas = datas.stream().filter(x -> unProcessedIds.contains(x.getId())).collect(Collectors.toList());
                for (IObjectData data : datas) {
                    String updateSql = String.format("UPDATE biz_leads SET biz_status='%s'," +
                                    " last_follow_time=%s,last_follower='%s',is_overtime=false" +
                                    " WHERE tenant_id='%s'" +
                                    " AND id='%s' AND biz_status='%s'", PROCESSED
                            , data.get(LAST_FOLLOW_TIME), data.get(LAST_FOLLOWER), tenantId, data.getId(), UN_PROCESSED);
                    log.info("update leads status processed sql {}", updateSql);
                    try {
                        customerFollowService.updateLeadsStatus(tenantId, data.getId(), data.get(LAST_FOLLOWER).toString(), data.get(LAST_FOLLOW_TIME, String.class));
                    } catch (Exception e) {
                        log.error("update leads status error {}", tenantId, e);
                    }
                    customerFollowService.sendRecalculateTask(tenantId, LEADS_OBJ, data.getId());
                    Optional<IObjectData> oriData = byIds.stream().filter(x -> x.getId().equals(data.getId())).findFirst();
                    if (oriData.isPresent()) {
                        String userId = data.get(LAST_FOLLOWER, String.class);
                        if (StringUtils.isBlank(userId)) {
                            userId = "-10000";
                        }
                        IObjectData tempData = oriData.get();
                        tempData.set("biz_status", PROCESSED);
                        addFlowRecord(tenantId, userId, Lists.newArrayList(tempData));
                    }
                }
                return leadsContents.stream().filter(x -> !unProcessedIds.contains(x.getObjectId())).collect(Collectors.toList());
            }
        } catch (MetadataServiceException e) {
            log.error("updateLeadsStatus error", e);
        }
        return leadsContents;
    }

    private void addFlowRecord(String tenantId, String userId, List<IObjectData> dataList) {

        List<IObjectData> oldFlowRecordDataList = Lists.newArrayList();
        User user = CommonUtils.buildUser(tenantId, userId);
        for (IObjectData leadsData : dataList) {
            String oldOwnerId = getOwner(leadsData);
            if (StringUtils.isBlank(oldOwnerId)) {
                continue;
            }
            SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
            searchTemplateQuery.setLimit(1);
            List<IFilter> filters = Lists.newArrayList();
            SearchUtil.fillFilterEq(filters, "leads_owner", oldOwnerId);
            SearchUtil.fillFilterEq(filters, "leads_id", leadsData.getId());
            searchTemplateQuery.setFilters(filters);
            List<OrderBy> orderByList = Lists.newArrayList();
            orderByList.add(new OrderBy("last_modified_time", false));
            searchTemplateQuery.setOrders(orderByList);
            searchTemplateQuery.setNeedReturnCountNum(false);
            searchTemplateQuery.setNeedReturnQuote(false);
            searchTemplateQuery.setPermissionType(0);

            IActionContext actionContext = ActionContextExt.of(user)
                    .disableDeepQuote()
                    .skipRelevantTeam()
                    .getContext();
            actionContext.setDoCalculate(false);

            QueryResult<IObjectData> queryResult = metaDataService.findBySearchQuery(actionContext, LEADS_FLOW_RECORD_OBJ, searchTemplateQuery);
            if (queryResult != null && org.apache.commons.collections.CollectionUtils.isNotEmpty(queryResult.getData())) {
                IObjectData oldFlowRecordData = queryResult.getData().get(0);
                oldFlowRecordData.set("leads_status", leadsData.get("biz_status", String.class));
                oldFlowRecordData.set("leads_status_changed_time", System.currentTimeMillis());
                oldFlowRecordData.set("last_modified_by", Lists.newArrayList(userId));
                oldFlowRecordData.set("last_modified_time", System.currentTimeMillis());
                oldFlowRecordDataList.add(oldFlowRecordData);
            }
        }
        if (CollectionUtils.notEmpty(oldFlowRecordDataList)) {
            List<String> updateFieldList = Lists.newArrayList("leads_status", "leads_status_changed_time", "last_modified_by",
                    "last_modified_time");
            try {
                objectDataPgService.batchUpdateWithField(oldFlowRecordDataList, updateFieldList, getDefaultActionContext(user, LEADS_FLOW_RECORD_OBJ));
            } catch (Exception e) {
                log.error("addFlowRecord error", e);
                throw new RuntimeException(e.getMessage());
            }
        }
    }

    private ActionContext getDefaultActionContext(User user, String apiName) {
        IObjectDescribe objectDescribe = null;
        try {
            objectDescribe = describeService.findObject(user.getTenantId(), apiName);
        } catch (Exception e) {
            log.error("findObject error:{}", user.getTenantId(), e);
        }
        ActionContext actionContext = new ActionContext();
        actionContext.setEnterpriseId(user.getTenantId());
        actionContext.setUserId(user.getUserId());
        actionContext.setDbType("pg");
        actionContext.setAllowUpdateInvalid(true);
        actionContext.put("not_validate", true);
        actionContext.setPrivilegeCheck(false);
        actionContext.setObjectDescribe(objectDescribe);
        return actionContext;
    }

}
