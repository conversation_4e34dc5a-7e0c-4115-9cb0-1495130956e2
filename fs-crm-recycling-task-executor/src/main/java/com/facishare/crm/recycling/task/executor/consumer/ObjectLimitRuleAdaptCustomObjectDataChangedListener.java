package com.facishare.crm.recycling.task.executor.consumer;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.sfa.lto.utils.TenantUtil;
import org.apache.rocketmq.common.message.MessageExt;
import com.facishare.crm.recycling.task.executor.common.SfaRecyclingTaskRateLimiterService;
import com.facishare.crm.recycling.task.executor.model.CustomObjectDataChangeMQMessage;
import com.facishare.crm.recycling.task.executor.service.EmployeeChangeOnChangeService;
import com.facishare.crm.recycling.task.executor.service.ObjectLimitEmployeeService;
import com.facishare.crm.recycling.task.executor.service.impl.ObjectLimitPartnerDataChangedService;
import com.facishare.paas.appframework.common.mq.RocketMQMessageListener;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.nio.charset.StandardCharsets;
import java.util.List;

import static com.facishare.crm.recycling.task.executor.util.ConstantUtils.PERSONNEL_OBJ;

/**
 * 执行器接收MQ入口
 */
@Slf4j
public class ObjectLimitRuleAdaptCustomObjectDataChangedListener implements RocketMQMessageListener {
    private static final String PARTNER_API_NAME = "PartnerObj";

    @Autowired
    private ObjectLimitPartnerDataChangedService objectLimitPartnerDataChangedService;

    @Autowired
    private SfaRecyclingTaskRateLimiterService sfaRecyclingTaskRateLimiterService;

	@Autowired
	private EmployeeChangeOnChangeService employeeChangeOnChangeService;

    @Autowired
    private ObjectLimitEmployeeService objectLimitEmployeeService;

    @Autowired
    private TenantUtil tenantUtil;

    private List<String> apiNames = Lists.newArrayList(PARTNER_API_NAME, PERSONNEL_OBJ);

    @Override
    public void consumeMessage(List<MessageExt> messages) {
        if (CollectionUtils.empty(messages)) {
            return;
        }

        for (MessageExt message : messages) {
			String entityId = null;
            if(CollectionUtils.notEmpty(message.getProperties()) && message.getProperties().containsKey("describe_api_name")) {
                entityId = message.getProperties().get("describe_api_name");
                if(!apiNames.contains(entityId)) {
                    continue;
                }
            }
            String body = new String(message.getBody(), StandardCharsets.UTF_8);
            CustomObjectDataChangeMQMessage objectDataChangeMQMessage = JSON.parseObject(body, CustomObjectDataChangeMQMessage.class);
			objectDataChangeMQMessage.setEntityId(entityId);
			log.info("msgId:{},ObjectLimitRuleAdaptCustomObjectDataChangedListener:{}", message.getMsgId(), objectDataChangeMQMessage);
			this.allocateRuleOnChange(objectDataChangeMQMessage);
            if (!"u".equals(objectDataChangeMQMessage.getOp())) {
                continue;
            }
            consumeMessage(objectDataChangeMQMessage);
        }
    }

    private void consumeMessage(CustomObjectDataChangeMQMessage message) {
        try {
            if (tenantUtil.isExclusiveCloudEnterprise(message.getTenantId())) {
                log.info("{} is exclusive cloud ei", message.getTenantId());
                return;
            }
            List<String> partnerIds = Lists.newArrayList();
            for (CustomObjectDataChangeMQMessage.Content msg : message.getBody()) {
                String describeApiName = msg.getEntityId();
                if (PARTNER_API_NAME.equals(describeApiName)) {
                    partnerIds.add(msg.getObjectId());
                }
            }
			if (PERSONNEL_OBJ.equals(message.getEntityId())){
				sfaRecyclingTaskRateLimiterService.getObjectLimitObjectDataLimiter().acquire();
				objectLimitEmployeeService.employeeCustomOnChanged(message);
			}
            if (CollectionUtils.notEmpty(partnerIds)) {
                sfaRecyclingTaskRateLimiterService.getObjectLimitObjectDataLimiter().acquire();
                objectLimitPartnerDataChangedService.execute(message.getTenantId(), partnerIds);
            }
        } catch (Exception e) {
            log.error("ObjectLimitRuleAdaptCustomObjectDataChangedListener consumeMessage error {}", message, e);
            throw new RuntimeException(e);
        }
    }

	private void allocateRuleOnChange(CustomObjectDataChangeMQMessage message) {
		try {
			if (PERSONNEL_OBJ.equals(message.getEntityId())) {
				employeeChangeOnChangeService.onChange(message);
			}
		} catch (Exception e) {
			log.error("监听人员变动消息消费失败！", e);
		}

	}
}
