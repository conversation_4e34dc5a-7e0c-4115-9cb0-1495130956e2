package com.facishare.crm.recycling.task.executor.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.appserver.checkinsoffice.api.model.GetUserAttendanceInfos;
import com.facishare.appserver.checkinsoffice.api.service.CheckinsOfficeV2Service;
import com.facishare.converter.EIEAConverter;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.recycling.task.executor.biz.CustomerBiz;
import com.facishare.crm.recycling.task.executor.common.RecyclingTaskGray;
import com.facishare.crm.recycling.task.executor.enums.*;
import com.facishare.crm.recycling.task.executor.model.*;
import com.facishare.crm.recycling.task.executor.service.RecalculateService;
import com.facishare.crm.recycling.task.executor.util.ConvertUtils;
import com.facishare.crm.recycling.task.executor.util.DateUtils;
import com.facishare.crm.recycling.task.executor.util.ObjectDataUtils;
import com.facishare.crm.recycling.task.executor.util.Pair;
import com.facishare.crm.sfa.audit.log.context.SFALogContext;
import com.facishare.crm.sfa.expression.SFAExpressionServiceImpl;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.service.IObjectDescribeService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.fxiaoke.paas.gnomon.api.NomonProducer;
import com.fxiaoke.paas.gnomon.api.entity.NomonDeleteMessage;
import com.fxiaoke.paas.gnomon.api.entity.NomonMessage;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

import static com.facishare.crm.recycling.task.executor.util.ConstantUtils.*;

/**
 * @Description
 * <AUTHOR>
 * @Date 2019-02-25 17:02
 */

@Slf4j
@Component
public abstract class AbstractRecalculate implements RecalculateService {

    @Autowired
    private CustomerBiz customerBiz;

    @Autowired
    private NomonProducer nomonProducer;

    @Autowired
    private IObjectDescribeService objectDescribeService;

    @Autowired
    private SFAExpressionServiceImpl sfaExpressionService;

    @Autowired
    private RecyclingTaskGray recyclingTaskGray;

    @Autowired
    private CheckinsOfficeV2Service checkinsOfficeV2Service;
    ;

    @Autowired
    private EIEAConverter converter;


    @Override
    public void execute(RecalculateMessage message) {

        // 规则修改，直接返回,规则中的 objectId 为线索池/公海 id，不需要走这里的逻辑
        if (ActionCodeEnum.CHANGE_RULE.getActionCode().equals(message.getActionCode())) {
            return;
        }
        if (recyclingTaskGray.skipTenantId(message.getTenantId())) {
            return;
        }

        IObjectData objectData = customerBiz.getObjectById(message.getTenantId(), message.getObjectId(), message.getObjectApiName());
        //只针对客户动作做处理
        if (ApiNameEnum.ACCOUNT_OBJ.getApiName().equals(message.getObjectApiName())) {
            if (objectData != null) {
                log.info("customer id:{},tenantId:{},actionCode:{},biz_status:{},life_status:{},highSeasId:{}",
                        message.getObjectId(), message.getTenantId(), message.getActionCode(), objectData.get(BIZ_STATUS), objectData.get(LIFE_STATUS), objectData.get(HIGH_SEAS_ID));
                message.setObjectData(objectData);
            }
        } else if (ApiNameEnum.LEADS_OBJ.getApiName().equals(message.getObjectApiName()) || ApiNameEnum.LEADS_POOL_OBJ.getApiName().equals(message.getObjectApiName())) {
            if (objectData != null) {
                log.info("leads name:{},tenantId:{},actionCode:{}", objectData.getName(), message.getTenantId(), message.getActionCode());
                //非线索池线索，不受回收规则影响
                if (objectData.get(LEADS_POOL_ID) == null || StringUtils.isBlank(objectData.get(LEADS_POOL_ID).toString())) {
                    log.warn("return, leads_pool_id is null :{}", objectData.getId());
                    return;
                }
                objectData.set(LAST_FOLLOWED_TIME, objectData.get(LAST_FOLLOW_TIME));
                message.setObjectData(objectData);
            }
        }
    }




    /**
     * 根据规则计算到期时间
     * 影响线索的到期时间：
     * 线索池ID、最后跟进时间、转换时间、影响规则计算时间、负责人变更时间、领取时间、分配时间、规则最后修改时间、是否重新计算、规则上转换天数和跟进天数
     * <p>
     * 影响客户的到期时间：
     *
     * @param recyclingRule
     * @param objectData
     * @return
     */
    public Pair<Date, Integer> calculateTime(RecyclingRule recyclingRule, IObjectData objectData, String apiName, RecyclingMessage recyclingMessage) {
//        Date date = Date.from(LocalDateTime.of(LocalDate.now(), LocalTime.MIN).toInstant(ZoneId.systemDefault()))
        Integer followUpDays = recyclingRule.getFollowUpDays();
        Integer dealDays = recyclingRule.getDealDays();
        Date expireTime = new Date();
        int days = 0;
        int recyclingReasonType = RecyclingReasonEnum.FOLLOW.getType();
        //如果不包含过去天数
        if (!recyclingRule.isIsIncludePastTime()) {
            // 只设置了n天未跟进 或者 n天未成交
            if (0 == followUpDays && 0 != dealDays) {
                days = dealDays;
                expireTime = getDealLastTime(objectData, recyclingRule, days, apiName);
                log.info(" {} 天没有成交 ,最后expireTime为:{} ,{}", dealDays, expireTime, objectData.getId());
                recyclingReasonType = getRecyclingReasonType(RecyclingReasonEnum.DEAL.getType(), apiName);
                // 两个都是0，没有设置收回规则
            } else if (0 == dealDays && 0 != followUpDays) {
                days = followUpDays;
                expireTime = getFollowLastTime(objectData, recyclingRule, days, apiName);
                log.info(" {} 天没有跟进 ,最后expireTime为:{} ,{}", followUpDays, expireTime, objectData.getId());
            } else if (0 == followUpDays) {
                log.info(" no filter followUpDays:{}, dealDays:{},最后expireTime为:{} ,{}", followUpDays, dealDays, expireTime, objectData.getId());
                return null;
            } else {
                //都不为0 N天没有跟进，M天没有新的成交 选择最早的到期时间
                Date lastFollowTime = getFollowLastTime(objectData, recyclingRule, followUpDays, apiName);
                Date lastDealTime = getDealLastTime(objectData, recyclingRule, dealDays, apiName);
                if (lastDealTime.compareTo(lastFollowTime) > 0) {
                    days = followUpDays;
                    expireTime = lastFollowTime;
                } else {
                    days = dealDays;
                    recyclingReasonType = getRecyclingReasonType(RecyclingReasonEnum.DEAL.getType(), apiName);
                    expireTime = lastDealTime;
                }
                log.info(" {}天没有跟进，{}天没有成交 ,最后expireTime为:{} ,{},reasonType:{}", followUpDays, dealDays, expireTime, objectData.getId(), recyclingReasonType);
            }
            //包含过去天数
        } else {
            // 只设置了n天未跟进
            if (0 != followUpDays && dealDays == 0) {
                expireTime = getFollowLastTime(objectData, null, followUpDays, apiName);
                days = followUpDays;
                log.info("包含过去天数 {} 天没有跟进,最后expireTime为:{},{}", followUpDays, expireTime, objectData.getId());
                //只设置了n天未成交
            } else if (followUpDays == 0 && dealDays != 0) {
                expireTime = getDealLastTime(objectData, null, dealDays, apiName);
                log.info("包含过去天数 {} 天未成交,最后expireTime为:{},{}", dealDays, expireTime, objectData.getId());
                days = dealDays;
                recyclingReasonType = getRecyclingReasonType(RecyclingReasonEnum.DEAL.getType(), apiName);
                //N天没有跟进，M天没有新的成交
            } else if (followUpDays != 0 && dealDays != 0) {
                Date lastFollowTime = getFollowLastTime(objectData, null, followUpDays, apiName);
                Date lastDealTime = getDealLastTime(objectData, null, dealDays, apiName);
                if (lastDealTime.compareTo(lastFollowTime) > 0) {
                    days = followUpDays;
                    expireTime = lastFollowTime;
                } else {
                    days = dealDays;
                    recyclingReasonType = getRecyclingReasonType(RecyclingReasonEnum.DEAL.getType(), apiName);
                    expireTime = lastDealTime;
                }
                log.info("包含过去天数 {} 天没有跟进，{}天没有成交 ,最后expireTime为:{},{},reasonType:{}", followUpDays, dealDays, expireTime, objectData.getId(), recyclingReasonType);
            }
        }
        recyclingMessage.setRecyclingDays(days);
        recyclingMessage.setRecyclingReasonType(recyclingReasonType);
        return new Pair<>(expireTime, days);
    }


    /**
     * 只设置了N天未跟进且包含过去天数
     * 获取 （规则最后修改时间、最后跟进时间、领取时间（公海客户）或负责人变更时间（非公海客户）、影响规则计算时间 ）中最近的时间
     *
     * @param objectData
     */
    public Date getFollowLastTime(IObjectData objectData, RecyclingRule recyclingRule, long days, String apiName) {
        if (ApiNameEnum.LEADS_OBJ.getApiName().equals(apiName)) {
            return getLeadsLastTime(objectData, recyclingRule, days, true);
        }
        return getLastTime(objectData, recyclingRule, days, true);
    }


    /**
     * 只设置了N天未成交且包含过去天数
     * 获取 （规则最后修改时间、成交时间、领取时间（公海客户）或负责人变更时间（非公海客户）、影响规则计算时间 ）中最近的时间为基准时间+N天
     *
     * @param objectData
     */
    public Date getDealLastTime(IObjectData objectData, RecyclingRule recyclingRule, long days, String apiName) {
        if (ApiNameEnum.LEADS_OBJ.getApiName().equals(apiName)) {
            return getLeadsLastTime(objectData, recyclingRule, days, false);
        }
        return getLastTime(objectData, recyclingRule, days, false);
    }


    /**
     * 客户对象时间计算，取最大时间戳
     *
     * @param objectData
     * @param recyclingRule
     * @param days
     * @param isFollow      true:n天未跟进，false:n天未成交
     * @return
     */
    public Date getLastTime(IObjectData objectData, RecyclingRule recyclingRule, long days, boolean isFollow) {
        // 规则最后修改时间
        long updateTime;
        if (recyclingRule == null) {
            updateTime = 0L;
        } else {
            updateTime = recyclingRule.getUpdateTime();
        }

        boolean isHighSeasAccount = objectData.get(HIGH_SEAS_ID) != null && !StringUtils.isBlank(objectData.get(HIGH_SEAS_ID, String.class));
        // 最近跟进或者成交时间
        long lastFollowedDealTime;
        // 领取时间（公海客户）或负责人变更时间
        long claimedOrOwnerChangedTime;

        if (isFollow) {
            // 最后跟进时间
            lastFollowedDealTime = objectData.get(LAST_FOLLOWED_TIME) == null ? 0L : (long) objectData.get(LAST_FOLLOWED_TIME);
        } else {
            // 最后成交时间
            lastFollowedDealTime = objectData.get(LAST_DEAL_CLOSED_TIME) == null ? 0L : (long) objectData.get(LAST_DEAL_CLOSED_TIME);
        }

        if (isHighSeasAccount) {
            // 领取时间
            claimedOrOwnerChangedTime = objectData.get(CLAIMED_TIME) == null ? 0L : (long) objectData.get(CLAIMED_TIME);
        } else {
            //  负责人变更时间
            claimedOrOwnerChangedTime = objectData.get(OWNER_MODIFIED_TIME) == null ? 0L : (long) objectData.get(OWNER_MODIFIED_TIME);
        }

        ArrayList<Long> list =
                (ArrayList<Long>) Lists.newArrayList(updateTime, lastFollowedDealTime, claimedOrOwnerChangedTime)
                        .stream().sorted().collect(Collectors.toList());
        log.info("objectId:{} getLastTime ,updateTime:{} ,lastFollowedDealTime:{}, claimedOrOwnerChangedTime:{}, {} ",
                objectData.getId(), updateTime, lastFollowedDealTime, claimedOrOwnerChangedTime, list.get(list.size() - 1));
        return DateUtils.afterDays(new Date(list.get(list.size() - 1)), days);
    }

    /**
     * 线索对象时间计算，取最大时间戳
     *
     * @param objectData
     * @param recyclingRule null： 包含过去天数；
     * @param days
     * @param isFollow      true:n天未跟进，false:n天未成交
     * @return
     */
    public Date getLeadsLastTime(IObjectData objectData, RecyclingRule recyclingRule, long days, boolean isFollow) {
        // 规则最后修改时间
        Long updateTime;
        if (recyclingRule == null) {
            updateTime = 0L;
        } else {
            updateTime = recyclingRule.getUpdateTime();
        }

        // 最近跟进或者转换时间
        Long lastFollowedDealTime = 0L;
        // 负责人变更时间
        Long claimedOrOwnerChangedTime = 0L;

        if (isFollow) {
            // 最后跟进时间
            lastFollowedDealTime = objectData.get(LAST_FOLLOW_TIME) == null ? 0L : (Long) objectData.get(LAST_FOLLOW_TIME);
        } else {
            // 最后转换时间
            lastFollowedDealTime = objectData.get(TRANSFORM_TIME) == null ? 0L : (Long) objectData.get(TRANSFORM_TIME);
            // 负责人变更时间
            claimedOrOwnerChangedTime = objectData.get(OWNER_CHANGE_TIME) == null ? 0L : (Long) objectData.get(OWNER_CHANGE_TIME);
        }

        ArrayList<Long> list =
                (ArrayList<Long>) Lists.newArrayList(updateTime, lastFollowedDealTime, claimedOrOwnerChangedTime)
                        .stream().sorted().collect(Collectors.toList());
        log.info("getLeadsLastTime isFollow:{} objectId:{},updateTime:{},lastFollowedDealTime:{},claimedOrOwnerChangedTime:{}, {} ",
                isFollow, objectData.getId(), updateTime, lastFollowedDealTime, claimedOrOwnerChangedTime, list.get(list.size() - 1));
        return DateUtils.afterHours(new Date(list.get(list.size() - 1)), days);
    }


    /**
     * 删除回收task，同时删除提醒
     */
    public void delete(String tenantId, String objectId) {
        log.info("delete recycling and remind nomonTask :{},{}", tenantId, objectId);
        deleteRecycling(tenantId, objectId);
        deleteRemind(tenantId, objectId);
    }

    /**
     * 删除回收任务
     *
     * @param tenantId
     * @param objectId
     */
    public void deleteRecycling(String tenantId, String objectId) {
        NomonDeleteMessage nomonDeleteMessage = NomonDeleteMessage.builder()
                .biz(OBJ_RECYCLING_BIZ)
                .tenantId(tenantId)
                .dataId(objectId)
                .build();
        nomonProducer.send(nomonDeleteMessage);
    }

    /**
     * 删除提醒任务
     *
     * @param tenantId
     * @param objectId
     */
    public void deleteRemind(String tenantId, String objectId) {
        NomonDeleteMessage nomonDeleteRemindMessage = NomonDeleteMessage.builder()
                .biz(OBJ_REMIND_BIZ)
                .tenantId(tenantId)
                .dataId(objectId)
                .build();
        nomonProducer.send(nomonDeleteRemindMessage);
    }


    private void sendRecycling(String tenantId, String objectId, String apiName, Date executeTime, RecyclingRuleInfoModel recyclingRule, RecyclingMessage recyclingMessage) {
        Integer callQueueMod = Math.abs(tenantId.concat(OBJ_RECYCLING_BIZ).hashCode());
        RecyclingMessage message = RecyclingMessage.builder()
                .tenantId(tenantId)
                .objectId(objectId)
                .objectApiName(apiName)
                .targetId(recyclingRule.getTargetPoolId())
                .functionApiName(recyclingRule.getFunctionApiName())
                .recyclingReasonType(recyclingMessage.getRecyclingReasonType())
                .recyclingDays(recyclingMessage.getRecyclingDays())
                .build();
        NomonMessage nomonMessage = NomonMessage.builder()
                .biz(OBJ_RECYCLING_BIZ)
                .tenantId(tenantId)
                .dataId(objectId)
                .executeTime(executeTime)
                .callArg(JSONObject.toJSON(message).toString())
                .callQueueMod(callQueueMod)
                .build();
        nomonProducer.send(nomonMessage);
        log.info("send nomonMessage:{}", nomonMessage);
    }

    /**
     * 新建/更新 到时间收回的task
     *
     * @param tenantId
     * @param objectId    客户id
     * @param apiName
     * @param executeTime 回收时间
     */
    public void send(String tenantId, String objectId, String apiName, Date executeTime, String highSeasId) {
        RecyclingMessage recyclingMessage = new RecyclingMessage();
        recyclingMessage.setTenantId(tenantId);
        recyclingMessage.setObjectId(objectId);
        recyclingMessage.setObjectApiName(apiName);
        recyclingMessage.setTargetId(highSeasId);

        NomonMessage nomonMessage = NomonMessage.builder()
                .biz(OBJ_RECYCLING_BIZ)
                .tenantId(tenantId)
                .dataId(objectId)
                .executeTime(executeTime)
                .callArg(JSONObject.toJSON(recyclingMessage).toString())
                .build();
        nomonProducer.send(nomonMessage);
        log.info("send nomonMessage:{}", nomonMessage);
    }


    /**
     * 新建/更新 到时间收回的task
     *
     * @param tenantId
     * @param objectId    客户id
     * @param apiName
     * @param executeTime 回收时间
     */
    public void sendRecalculate(String tenantId, String objectId, String apiName, Date executeTime) {
        RecalculateMessage recalculateMessage = new RecalculateMessage();
        recalculateMessage.setTenantId(tenantId);
        recalculateMessage.setActionCode(ActionCodeEnum.FOLLOW.getActionCode());
        recalculateMessage.setObjectId(objectId);
        recalculateMessage.setObjectApiName(apiName);


        NomonMessage nomonMessage = NomonMessage.builder()
                .biz("crm_object_recycling_task_recalculate")
                .tenantId(tenantId)
                .dataId(objectId)
                .executeTime(executeTime)
                .callArg(JSONObject.toJSON(recalculateMessage).toString())
                .build();
        log.info("send,nomonMessage:{}", nomonMessage.toString());
        nomonProducer.send(nomonMessage);
    }

    /**
     * 发送提醒
     *
     * @param tenantId
     * @param objectId
     * @param apiName
     * @param executeTime
     * @param highSeasId
     */
    public void sendRemindTask(String tenantId, String objectId, String apiName, Date executeTime, String highSeasId) {
        RemindMessage remindMessage = new RemindMessage();
        remindMessage.setTenantId(tenantId);
        remindMessage.setObjectApiName(apiName);
        remindMessage.setObjectId(objectId);


        NomonMessage nomonMessage = NomonMessage.builder()
                .biz("crm_object_recycling_task_remind")
                .tenantId(tenantId)
                .dataId(objectId)
                .executeTime(executeTime)
                .callArg(JSONObject.toJSON(remindMessage).toString())
                .build();
        nomonProducer.send(nomonMessage);
        log.info("send,nomonMessage:{}", nomonMessage);
    }


    /**
     * @param remindMessage 提醒消息
     */
    public void sendRemindTask(RemindMessage remindMessage) {

        List<RuleTypeRemindTime> remindTimes = remindMessage.getRuleTypeRemindTimes();
        if (CollectionUtils.isEmpty(remindTimes)) {
            return;
        }

        long currentTime = System.currentTimeMillis();
        // 提醒时间≥ (当前时间 - 20mins)，排序处理
        List<RuleTypeRemindTime> afterRemindTimes = remindTimes.stream().filter(x -> x.getRemindTime() != null && x.getRemindTime().compareTo(currentTime - 1000 * 60 * 20) >= 0)
                .sorted(Comparator.comparing(RuleTypeRemindTime::getRemindTime)).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(afterRemindTimes)) {
            deleteRemind(remindMessage.getTenantId(), remindMessage.getObjectId());
            return;
        }
        remindMessage.setRemindTime(new Date(afterRemindTimes.get(0).getRemindTime()));
        remindMessage.setCreateTime(new Date());
        if (Utils.LEADS_API_NAME.equals(remindMessage.getObjectApiName())) {
            remindMessage.setNewVersion("true");
        }

        remindMessage.setRuleTypeRemindTimes(afterRemindTimes);
        NomonMessage nomonMessage = NomonMessage.builder()
                .biz("crm_object_recycling_task_remind")
                .tenantId(remindMessage.getTenantId())
                .dataId(remindMessage.getObjectId())
                .executeTime(remindMessage.getRemindTime())
                .callArg(JSONObject.toJSON(remindMessage).toString())
                .build();
        nomonProducer.send(nomonMessage);
        log.info("send remind nomonMessage:{}", nomonMessage);
    }


    /**
     * 计算回收规则里面的提醒时间
     *
     * @param recyclingRule
     * @param expireTime
     * @param ruleTypeRemindTime
     * @param apiName
     * @return
     */
    @Deprecated
    public Date calculateRecyclingRemindTime(RecyclingRule recyclingRule, Date expireTime, RuleTypeRemindTime ruleTypeRemindTime, String apiName) {
        List<RecyclingRemindRule> remindRules = recyclingRule.getRecyclingRemindRuleList();

        if (CollectionUtils.isEmpty(remindRules)) {
            return null;
        } else if (remindRules.size() > 1) {
            log.warn("RecyclingRemindRules not 1:{}", remindRules);
        }
        String ruleType = RuleTypeEnum.RECYCLING.getValue();
        Date remindTime = new Date();
        int remindDays = remindRules.get(0).getRemindDays();
        ruleTypeRemindTime.setDays(remindDays);
        ruleTypeRemindTime.setRuleType(ruleType);
        if (Utils.LEADS_API_NAME.equals(apiName)) {
            remindTime = DateUtils.beforeHours(expireTime, remindDays);
        } else {
            remindTime = DateUtils.beforeDays(expireTime, remindDays);
        }
        ruleTypeRemindTime.setRemindTime(remindTime.getTime());
        return remindTime;
    }

    public Date calculateRecyclingRemindTime(Integer remindDays, Date expireTime, RuleTypeRemindTime ruleTypeRemindTime, String apiName) {
        String ruleType = RuleTypeEnum.RECYCLING.getValue();
        Date remindTime;
        ruleTypeRemindTime.setDays(remindDays);
        ruleTypeRemindTime.setRuleType(ruleType);
        if (Utils.LEADS_API_NAME.equals(apiName)) {
            remindTime = DateUtils.beforeHours(expireTime, remindDays);
        } else {
            remindTime = DateUtils.beforeDays(expireTime, remindDays);
        }
        ruleTypeRemindTime.setRemindTime(remindTime.getTime());
        return remindTime;
    }


    /**
     * 客户状态是 已分配 且 生命状态是 (正常 或者 变更中)  返回 true
     *
     * @param objectData
     * @return
     */
    protected Boolean isNeedRecalculate(IObjectData objectData) {
        boolean needRecalculate = false;
        if (objectData == null
                || StringUtils.isBlank(objectData.get(BIZ_STATUS, String.class))
                || StringUtils.isBlank(objectData.get(LIFE_STATUS, String.class))) {
            return false;
        }

        if (BizStatusEnum.ALLOCATED.getValue().equals(objectData.get(BIZ_STATUS, String.class))
                && (LifeStatusEnum.NORMAL.getValue().equals(objectData.get(LIFE_STATUS, String.class))
                || LifeStatusEnum.IN_CHANGE.getValue().equals(objectData.get(LIFE_STATUS, String.class))
        )) {
            needRecalculate = true;
        }
        // 新预防产生垃圾数据处理

        if (BizStatusEnum.UNALLOCATED.getValue().equals(objectData.get(BIZ_STATUS, String.class))
                && objectData.get(EXPIRE_TIME) != null) {
            log.warn("customer biz_status is unallocated and expire_time is not null,  clear expire_time:{}", objectData.getId());
            customerBiz.clearExpireTime(objectData);
            delete(objectData.getTenantId(), objectData.getId());
        }
        return needRecalculate;
    }


    /**
     * 获取匹配的回收规则id
     *
     * @param recyclingRules
     * @param objectData
     * @param apiName
     * @return
     */
    public String getMappingRuleId(List<RecyclingRuleInfoModel> recyclingRules, IObjectData objectData, String apiName) {
        // todo getdescribe move before
        IObjectDescribe objectDescribe = null;
        try {
            objectDescribe = objectDescribeService.findByTenantIdAndDescribeApiName(objectData.getTenantId(), apiName);
        } catch (MetadataServiceException e) {
            log.error("findByTenantIdAndDescribeApiName error", e);
        }
        if (objectDescribe == null) {
            return null;
        }
        // todo sorted should on db
        recyclingRules = recyclingRules.stream().filter(x -> x.getRecyclingRuleType() != 1)
                .sorted(Comparator.comparing(RecyclingRuleInfoModel::getPriority)).collect(Collectors.toList());
        for (RecyclingRuleInfoModel recyclingRule : recyclingRules) {
            //获取公海规则filter
            if (newMappingRule(objectData, objectDescribe, recyclingRule)) {
                if (recyclingRule.getRecyclingRuleType() == 1) {
                    return null;
                }
                return recyclingRule.getId();
            }
        }
        return null;
    }


    /**
     * 根据规则计算到期时间和到期提醒时间
     * todo 查询描述优化：1. 避免查询；2. 缓存
     *
     * @param tenantId
     * @param objectData     客户数据
     * @param recyclingRules 规则(包含公海，非公海)
     */
    public Pair<Date, Date> calculateExpireTimeByNewRule(String tenantId, String apiName, IObjectData objectData,
                                                         List<RecyclingRuleInfoModel> recyclingRules,
                                                         RuleTypeRemindTime ruleTypeRemindTime,
                                                         Double extendDays) {
        String objectId = objectData.getId();
        IObjectData iObjectDataOrigin = ObjectDataExt.of(objectData).copy();
        if (CollectionUtils.isNotEmpty(recyclingRules)) {
            //优先级排序
            recyclingRules = recyclingRules.stream().sorted(Comparator.comparing(RecyclingRuleInfoModel::getPriority)).collect(Collectors.toList());
            log.info("calculateExpireTime objectId:{},{},sorted recyclingrule:{}", objectData.getId(), apiName, recyclingRules.size());
        } else {
            SFALogContext.putVariable("extra1", "recyclingRules is empty");
            SFALogContext.putVariable("extra2", "delete expireTime");
            log.info("calculateExpireTime delete recycling task, objectId:{},{},tenantId:{}", objectData.getId(), apiName, tenantId);
            // 没有任何规则的时候会删除
            deleteRecycling(tenantId, objectId);
            //清空到期时间
            customerBiz.clearExpireTime(objectData);
            return null;
        }
        IObjectDescribe objectDescribe = getIObjectDescribe(tenantId, apiName);
        if (objectDescribe == null) return null;
        RecyclingMessage recyclingMessage = RecyclingMessage.builder().build();
        for (RecyclingRuleInfoModel recyclingRule : recyclingRules) {
            //获取规则filter
            if (!newMappingRule(objectData, objectDescribe, recyclingRule)) {
                continue;
            }
            // 匹配到规则，不需要回收，跳出循环，删除到期时间
            if (recyclingRule.getRecyclingRuleType() == 1) {
                log.warn("recycling-noNeedRecycling-dataId={},recyclingRuleId={},priority={},mq-p", objectId, recyclingRule.getId(), recyclingRule.getPriority());
                break;
            }
            // 计算到期时间
            Pair<Date, Integer> expireTimeAndDays = new Pair<>(new Date(), 0);
            Date expireTime = null;
            // 如果只是申请延期，
            if (extendDays != null) {
                //没有跳过节假日，直接加上延期天数
                if ((!recyclingTaskGray.expireTimeSkipHolidays(tenantId) || !recyclingRule.getSkipHolidays()) && StringUtils.isNotBlank(objectData.get(EXPIRE_TIME, String.class))) {
                    expireTime = new Date(objectData.get(EXPIRE_TIME, Long.class));
                } else {
                    expireTimeAndDays = calculateTime(ConvertUtils.convert2Old(recyclingRule), objectData, apiName, recyclingMessage);
                    expireTime = expireTimeAndDays.getKey();
                    log.info("extend expireTime the old expireTime is null {},{},extendDays:{}", expireTime, objectData.getId(), extendDays);
                }
                expireTime = DateUtils.afterDays(expireTime, extendDays);
                log.info("extend expireTime the last expireTime is:{},{},extendDays:{}", expireTime, objectData.getId(), extendDays);
            } else {
                log.warn("extendDays is null tenantId:{},objectId:{}", objectData.getTenantId(), objectData.getId());
                expireTimeAndDays = calculateTime(ConvertUtils.convert2Old(recyclingRule), objectData, apiName, recyclingMessage);
                if (expireTimeAndDays == null) {
                    continue;
                }
                expireTime = expireTimeAndDays.getKey();
                //申请延期灰度企业，增加延期天数。 有延期天数，旧的到期时间大于当前新跟进后的到期时间，则不会更新替换。
                if (recyclingTaskGray.isExpireTimeExtend(objectData.getTenantId())) {
                    String extendDaysStr = objectData.get(EXTEND_DAYS, String.class);
                    if (extendDaysStr != null) {
                        extendDays = Double.valueOf(extendDaysStr);
                    }
                    Long oldExpireTime = objectData.get(EXPIRE_TIME, Long.class) == null ? 0L : objectData.get(EXPIRE_TIME, Long.class);
                    Long newExpireTime = expireTime.getTime();
                    if (extendDays != null && extendDays > 0 && newExpireTime <= oldExpireTime) {
                        log.info("newExpireTime < oldExpireTime continue gray tenantId:{},objectId:{},extendDays:{},oldExpireTime:{},newExpireTime:{}",
                                objectData.getTenantId(), objectData.getId(), extendDays, oldExpireTime, newExpireTime);

                        // 灰度了跳过节假日，取未跳过节假日的延期时间，重新计算节假日
                        if (recyclingTaskGray.expireTimeSkipHolidays(tenantId) && recyclingRule.getSkipHolidays()) {
                            expireTime = DateUtils.afterDays(expireTime, extendDays);
                        } else {
                            expireTime = new Date(oldExpireTime);
                        }
                    } else {
                        log.info("newExpireTime > oldExpireTime continue update expireTime tenantId:{},objectId:{},extendDays:{},oldExpireTime:{},newExpireTime:{}",
                                objectData.getTenantId(), objectData.getId(), extendDays, oldExpireTime, newExpireTime);
                    }
                }
            }
            SFALogContext.putVariable("extra1", recyclingRule.toString());
            log.warn("recycling-needRecycling-dataId={},recyclingRuleId={},priority={},mq-p", objectId, recyclingRule.getId(), recyclingRule.getPriority());
            String expireTimeLog = expireTime.toString();
            if (recyclingTaskGray.expireTimeSkipHolidays(tenantId) && recyclingRule.getSkipHolidays()) {
                expireTime = getExpireTimeSkipHolidays(expireTime, expireTimeAndDays.getValue(), iObjectDataOrigin);
                expireTimeLog += "; skipHolidays:" + expireTime.toString();
            }
            SFALogContext.putVariable("extra2", "expireTime: " + expireTimeLog);

            //创建收回的task
            sendRecycling(tenantId, objectId, apiName, expireTime, recyclingRule, recyclingMessage);
            Integer remindDays = null;
            Date remindTime = null;
            if (CollectionUtils.isNotEmpty(recyclingRule.getRecyclingRemindRuleList())) {
                remindDays = recyclingRule.getRecyclingRemindRuleList().get(0).getRemindDays();
                remindTime = calculateRecyclingRemindTime(remindDays, expireTime, ruleTypeRemindTime, apiName);
            }
            customerBiz.updateExpireTime(objectData, expireTime.getTime(), remindDays);
            return new Pair<>(expireTime, remindTime);
        }
        customerBiz.clearExpireTime(objectData);
        deleteRecycling(tenantId, objectId);
        return null;
    }

    @Nullable
    private IObjectDescribe getIObjectDescribe(String tenantId, String apiName) {
        IObjectDescribe objectDescribe;
        try {
            objectDescribe = objectDescribeService.findByTenantIdAndDescribeApiName(tenantId, apiName);
            if (objectDescribe == null) {
                return null;
            }
        } catch (MetadataServiceException e) {
            log.error("calculateExpireTime findByTenantIdAndDescribeApiName:{},{}", tenantId, apiName, e);
            throw new RuntimeException(e);
        }
        return objectDescribe;
    }


    public boolean newMappingRule(IObjectData objectData, IObjectDescribe objectDescribe, RecyclingRuleInfoModel recyclingRule) {
        return sfaExpressionService.evaluate(recyclingRule.getWheres(), objectData, objectDescribe);
    }

    /**
     * 根据回收里面的到期提前提醒配置，计算该提醒发出的时间。(还有多少天将被回收)
     *
     * @param recyclingRule      匹配到的规则
     * @param expireTime         数据下的到期时间
     * @param ruleTypeRemindTime
     * @param apiName
     * @return
     */
    public Date newCalculateRemindTime(RecyclingRuleInfoModel recyclingRule, Date expireTime, RuleTypeRemindTime ruleTypeRemindTime, String apiName) {
        List<RecyclingRemindRuleModel> remindRules = recyclingRule.getRecyclingRemindRuleList();

        if (CollectionUtils.isEmpty(remindRules)) {
            return null;
        }
        String ruleType = RuleTypeEnum.RECYCLING.getValue();
        Date remindTime = new Date();
        for (RecyclingRemindRuleModel remindRule : remindRules) {
            int remindDays = remindRule.getRemindDays();
            ruleTypeRemindTime.setDays(remindDays);
            ruleTypeRemindTime.setRuleType(ruleType);
            if (Utils.LEADS_API_NAME.equals(apiName)) {
                remindTime = DateUtils.beforeHours(expireTime, remindDays);
            } else {
                remindTime = DateUtils.beforeDays(expireTime, remindDays);
            }
        }
        return remindTime;
    }

    public Integer getRecyclingReasonType(Integer type, String apiName) {
        if (LEADS_OBJ.equals(apiName) && RecyclingReasonEnum.DEAL.getType() == type) {
            return RecyclingReasonEnum.TRANS.getType();
        }
        return type;
    }

    /**
     * @param minChangeTime
     * @param objectData
     * @return
     * @Description 跳过节假日返回跳过后的时间
     */
    public Date getExpireTimeSkipHolidays(Date minChangeTime, IObjectData objectData) {
        //Long oldExpireTime = objectData.get(EXPIRE_TIME, Long.class) == null ? 0L : objectData.get(EXPIRE_TIME, Long.class);
        // 取最大的到期时间，来跳过节假日。
        //long max = Math.max(oldExpireTime, expireTime.getTime());
        // 老的到期时间大于新的到期时间，取老的到期时间
        //if (max < System.currentTimeMillis()){
        //    return expireTime;
        //}
        Long remainingDays = getRemainingDays(minChangeTime, 0, objectData);
        return DateUtils.afterDays(minChangeTime, remainingDays);
    }

    /**
     * @param minChangeTime 最小的用于计算到期时间的时间。x天未跟进，y天未成交 跟进时间/成交时间的 最小时间 。
     * @param ruleDays      规则配置的 x天未跟进，y天未成交 里面的x 或者 y
     * @param objectData
     * @return
     */
    public Date getExpireTimeSkipHolidays(Date minChangeTime, Integer ruleDays, IObjectData objectData) {
        minChangeTime = DateUtils.beforeDays(minChangeTime, ruleDays);
        Long remainingDays;
        remainingDays = getRemainingDays(minChangeTime, ruleDays, objectData);
        return DateUtils.afterDays(DateUtils.afterDays(minChangeTime, ruleDays), remainingDays);
    }

    /**
     * 累计计算假期天数，
     *
     * @param expireTime 最后跟进时间或者最后成交时间
     * @param ruleDays
     * @param objectData
     * @return
     */
    public Long getRemainingDays(Date expireTime, Integer ruleDays, IObjectData objectData) {
        AtomicLong extendHolidays = new AtomicLong();
        AtomicLong holidays = new AtomicLong();
        // 条件为true，说明一直是假期，需要一直找到工作日/或者工作日不够，最多查找100000天
        // 最终：remainingDays == extendHolidays 终止计算

        // 初始值为保有的自然天数,比如是3个工作日后收回，后面累加假期天数，用于调用接口的参数估计
        AtomicLong remainingDays = new AtomicLong(ruleDays + 1);

        //// remainingDays 应该为（actualExpireTime - expireTime）的天数  + 规则天数
        //Long actualExpireTime = objectData.get(EXPIRE_TIME, Long.class);
        //if (actualExpireTime != null && actualExpireTime > expireTime.getTime()) {
        //    remainingDays.set((actualExpireTime - expireTime.getTime()) / (1000 * 60 * 60 * 24) + ruleDays);
        //}
        Date beginDate = expireTime;
        while (remainingDays.get() > 0 && holidays.get() <= 1000) {
            long days = getDays(remainingDays);
            beginDate = DateUtils.afterDays(beginDate, extendHolidays.get());
            // 重置
            extendHolidays.set(0);
            Date endDate = DateUtils.afterDays(beginDate, days);
            log.info("getRemainingDays id:{},getRemoteParams: beginDate:{},endDate:{},days:{}", objectData.getId(), beginDate, endDate, days);
            // 方便兼容: 如果接口没返回，则判定为工作日
            List<String> dateListsStr = DateUtils.getBetweenDateListsStr(beginDate, endDate);
            List<com.fxiaoke.common.Pair<String, GetUserAttendanceInfos.AttendanceInfo>> pairs = getRemotePairs(beginDate, objectData, days);
            if (pairs == null) {
                log.info("getRemainingDays id:{},return result is null, beginDate:{},endDate:{},days:{}", objectData.getId(), beginDate, endDate, days);
                return holidays.get();
            }
            log.info("id:{},expireTime:{},days:{}", objectData.getId(), expireTime, days);
            List<String> apiDateLists = pairs.stream().map(x -> x.first).collect(Collectors.toList());
            int index = 0;
            for (String dateStr : dateListsStr) {
                extendHolidays.getAndIncrement();
                if (!apiDateLists.contains(dateStr)) {
                    // 如果 apiDateLists 里面没有 dateStr，当作工作日处理
                    remainingDays.getAndDecrement();
                } else {
                    if (index < pairs.size()) {
                        com.fxiaoke.common.Pair<String, GetUserAttendanceInfos.AttendanceInfo> pair = pairs.get(index);
                        if (pair.second.getIsWork() == 0) {
                            holidays.getAndIncrement();
                        } else {
                            remainingDays.getAndDecrement();
                        }
                        index++;
                    }
                }
                if (remainingDays.get() == 0L) {
                    break;
                }
            }
        }
        log.info("id:{},extendHolidays:{},holidays:{}", objectData.getId(), extendHolidays.get(), holidays.get());
        return holidays.get();
    }



    /**
     * It returns the number of days to be added to the current date.
     *
     * @param remainingDays The number of days remaining in the subscription.
     * @return The number of days.
     */
    private long getDays(AtomicLong remainingDays) {
        long days = remainingDays.get();
        if (days < 10) {
            return Math.min(days + 20, 100);
        } else {
            return Math.min(Math.round(days * 1.5), 100);
        }
    }

    protected long getDays(Integer remainingDays) {
        if (remainingDays < 10) {
            return Math.min(remainingDays + 10, 100);
        } else {
            return Math.min(Math.round(remainingDays * 1.5), 100);
        }
    }

    protected List<com.fxiaoke.common.Pair<String, GetUserAttendanceInfos.AttendanceInfo>> getRemotePairs(Date beginDate, IObjectData objectData, long days) {
        Integer userId = ObjectDataUtils.getOwnerInteger(objectData);
        if (userId == null) {
            return null;
        }
        GetUserAttendanceInfos.Args args = new GetUserAttendanceInfos.Args();
        args.setStartDateStr(DateUtils.dateFormat(beginDate, DateUtils.DATE_FORMAT_YYYY_MM_DD));
        args.setEndDateStr(DateUtils.afterDaysFormat(beginDate, days));
        args.setUserIds(Sets.newHashSet(userId));

        GetUserAttendanceInfos.Result userAttendanceInfos = null;
        try {
            userAttendanceInfos = checkinsOfficeV2Service.getUserAttendanceInfos(getEa(objectData.getTenantId()), args);
            log.info("objectDataID:{}, userAttendanceInfos:{},args:{}", objectData.getId(), JSON.toJSONString(userAttendanceInfos), args);
        } catch (Exception e) {
            log.error("objectDataID:{}, userAttendanceInfos:{}", objectData.getId(), JSON.toJSONString(userAttendanceInfos), e);
            throw new RuntimeException(e);
        }
        if (userAttendanceInfos == null || userAttendanceInfos.getUserAttendanceInfoMap() == null) {
            return null;
        }
        return userAttendanceInfos.getUserAttendanceInfoMap().get(userId);
    }

    public List<com.fxiaoke.common.Pair<String, GetUserAttendanceInfos.AttendanceInfo>> getUserAttendanceInfos(WorkDaysModel.Arg arg) {
        GetUserAttendanceInfos.Args args = new GetUserAttendanceInfos.Args();
        args.setStartDateStr(arg.getBeginDate());
        args.setEndDateStr(arg.getEndDate());
        args.setUserIds(Sets.newHashSet(Integer.parseInt(arg.getUserId())));

        GetUserAttendanceInfos.Result userAttendanceInfos;
        try {
            userAttendanceInfos = checkinsOfficeV2Service.getUserAttendanceInfos(getEa(arg.getTenantId()), args);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        if (userAttendanceInfos == null || userAttendanceInfos.getUserAttendanceInfoMap() == null) {
            return new ArrayList<>();
        }
        return userAttendanceInfos.getUserAttendanceInfoMap().get(Integer.parseInt(arg.getUserId()));
    }

    public WorkDaysModel.Result getUserAttendanceDetails(WorkDaysModel.Arg arg) {
        List<com.fxiaoke.common.Pair<String, GetUserAttendanceInfos.AttendanceInfo>> userAttendanceInfos = getUserAttendanceInfos(arg);
        WorkDaysModel.Result result = new WorkDaysModel.Result();
        Integer sumHolidays = 0;
        ArrayList<WorkDaysModel.Detail> details = Lists.newArrayList();
        for (com.fxiaoke.common.Pair<String, GetUserAttendanceInfos.AttendanceInfo> userAttendanceInfo : userAttendanceInfos) {
            boolean isWork = userAttendanceInfo.second.getIsWork() == 1;
            WorkDaysModel.Detail detail = new WorkDaysModel.Detail();
            detail.setDateStr(userAttendanceInfo.first);
            detail.setIsWork(isWork);
            details.add(detail);
            if (!isWork) {
                sumHolidays++;
            }
        }
        result.setHolidays(sumHolidays);
        result.setDetails(details);
        return result;
    }

    private String getEa(String ei) {
        return converter.enterpriseIdToAccount(Integer.parseInt(ei));
    }
}


