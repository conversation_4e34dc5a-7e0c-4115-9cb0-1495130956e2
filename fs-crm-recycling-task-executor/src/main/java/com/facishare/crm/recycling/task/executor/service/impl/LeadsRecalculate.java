package com.facishare.crm.recycling.task.executor.service.impl;

import com.facishare.appserver.checkinsoffice.api.model.GetUserAttendanceInfos;
import com.facishare.crm.recycling.task.executor.biz.CustomerBiz;
import com.facishare.crm.recycling.task.executor.biz.LeadsBiz;
import com.facishare.crm.recycling.task.executor.biz.RecyclingBiz;
import com.facishare.crm.recycling.task.executor.common.RecyclingTaskGray;
import com.facishare.crm.recycling.task.executor.common.SfaRecyclingTaskRateLimiterService;
import com.facishare.crm.recycling.task.executor.enums.ActionCodeEnum;
import com.facishare.crm.recycling.task.executor.enums.ApiNameEnum;
import com.facishare.crm.recycling.task.executor.enums.LifeStatusEnum;
import com.facishare.crm.recycling.task.executor.model.RecalculateMessage;
import com.facishare.crm.recycling.task.executor.model.RecyclingRuleInfoModel;
import com.facishare.crm.recycling.task.executor.model.RemindMessage;
import com.facishare.crm.recycling.task.executor.model.RuleTypeRemindTime;
import com.facishare.crm.recycling.task.executor.util.DateUtils;
import com.facishare.crm.recycling.task.executor.util.Pair;
import com.facishare.crm.recycling.task.executor.util.SFAAuditLogRecalculateConvert;
import com.facishare.crm.sfa.audit.log.SFAAuditLog;
import com.facishare.crm.sfa.lto.enums.LeadsBizStatusEnum;
import com.facishare.paas.metadata.api.IObjectData;
import com.github.jedis.support.JedisCmd;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import static com.facishare.crm.recycling.task.executor.util.ConstantUtils.*;

/**
 * @Description
 * <AUTHOR>
 * @Date 2019-03-09 18:21
 */

@Component
@Slf4j
public class LeadsRecalculate extends AbstractRecalculate {

    @Autowired
    private LeadsBiz leadsBiz;

    @Autowired
    private RecyclingBiz recyclingBiz;

    @Autowired
    private SfaRecyclingTaskRateLimiterService sfaRecyclingTaskRateLimiterService;

    @Autowired
    private CustomerBiz customerBiz;

    @Autowired
    private RecyclingTaskGray recyclingTaskGray;

    @Autowired
    protected JedisCmd SFAJedisCmd;


    @SFAAuditLog(bizName = "#bizName", entityClass = RecalculateMessage.class, convertClass = SFAAuditLogRecalculateConvert.class, status = "#status",
            messageId = "#msg.msgId", extra = "#msg.reconsumeTimes", extra1 = "#extra1", extra2 = "#extra2")
    @Override
    public void execute(RecalculateMessage message) {
        super.execute(message);
        if (message.getObjectData() == null && !message.getActionCode().equals(ActionCodeEnum.CHANGE_RULE.getActionCode())) {
            log.info("LeadsRecalculate execute return:{},{}", message.getObjectId(), message.getTenantId());
            return;
        }
        switch (ActionCodeEnum.actionCodeOf(message.getActionCode())) {
            case CHANGE_RULE:
                recalculateByRule(message);
                break;
            case CHANGE_OWNER:
                recalculateByChangeOwner(message);
                break;
            case EXTEND:
                recalculateByExtend(message);
                break;
            case INVALID:
                recalculateByInvalid(message);
                break;
            case DEAL:
                recalculateByDeal(message);
                break;
            case RETURN:
            case RECYCLING:
                clear(message.getObjectData());
                break;
            case ADD:
            default:
                newRecalculateDefault(message);
                break;
        }
    }

    // 更换负责人
    private void recalculateByChangeOwner(RecalculateMessage message) {
        IObjectData objectData = message.getObjectData();
        log.info("leads recalculateByChangeOwner:{},tenantId:{},lifeStatus:{}", message.getObjectId(), message.getTenantId(), objectData.get(LIFE_STATUS));
        if (Boolean.TRUE.equals(worthCalculate(objectData))) {
            newRecalculateDefault(message);
        }
    }

    // 作废
    private void recalculateByInvalid(RecalculateMessage message) {
        IObjectData objectData = message.getObjectData();
        log.info("leads recalculateByInvalid:{},tenantId:{},lifeStatus:{}", message.getObjectId(), message.getTenantId(), objectData.get(LIFE_STATUS));
        if (!LifeStatusEnum.INVALID.getValue().equals(objectData.get(LIFE_STATUS))) {
            return;
        }
        clear(objectData);
    }

    // 转换
    private void recalculateByDeal(RecalculateMessage message) {
        IObjectData objectData = message.getObjectData();
        log.info("leads recalculateByDeal:{},tenantId:{},{}", message.getObjectId(), message.getTenantId(), objectData.get(BIZ_STATUS));
        if (!LeadsBizStatusEnum.TRANSFORMED.getValue().equals(objectData.get(BIZ_STATUS))) {
            return;
        }
        clear(objectData);
    }

    private void recalculateByExtend(RecalculateMessage message) {
        newRecalculateLeadsExtend(message.getTenantId(), message.getObjectData(), null, message.getExtendDays());
    }

    /**
     * 清空到期时间和删除task
     *
     * @param objectData
     */
    private void clear(IObjectData objectData) {
        if (objectData == null) {
            return;
        }
        if (StringUtils.isNotBlank(objectData.get(EXPIRE_TIME, String.class))) {
            customerBiz.clearExpireTime(objectData);
        }
        sfaRecyclingTaskRateLimiterService.getCrmRecalculateLimiter().acquire();
        delete(objectData.getTenantId(), objectData.getId());
        log.info("clear expire_time and delete task :{},{},{}", objectData.getTenantId(), objectData.getId(), objectData.getDescribeApiName());
    }


    private Boolean worthCalculate(IObjectData objectData) {
        if (objectData == null) {
            return false;
        }
        if (!LifeStatusEnum.NORMAL.getValue().equals(objectData.get(LIFE_STATUS)) &&
                !LifeStatusEnum.IN_CHANGE.getValue().equals(objectData.get(LIFE_STATUS))) {
            return false;
        }
        return !LeadsBizStatusEnum.UN_ASSIGNED.getValue().equals(objectData.get(BIZ_STATUS)) &&
                !LeadsBizStatusEnum.TRANSFORMED.getValue().equals(objectData.get(BIZ_STATUS));
    }


    /**
     * 计算收回时间,更新剩余保有时间
     */
    private void recalculateByRule(RecalculateMessage message) throws RuntimeException {
        log.info("LeadsObj recalculateByRule:{} ", message);
        List<RecyclingRuleInfoModel> recyclingRules = recyclingBiz.getRecyclingRule(message.getTenantId(), message.getObjectId(), LEADS_POOL_OBJ);
        String tenantId = message.getTenantId();
        String poolId = message.getObjectId();
        List<String> idLists = leadsBiz.getAllLeadsIdsByPoolId(tenantId, poolId);
        if (CollectionUtils.isEmpty(idLists)) {
            return;
        }
        List<IObjectData> objectDataList;
        int pageSize = 10; // 每页的大小
        int pageCount = (int) Math.ceil(idLists.size() / (double) pageSize); // 总页数

        for (int i = 0; i < pageCount; i++) {
            int fromIndex = i * pageSize;
            int toIndex = Math.min(fromIndex + pageSize, idLists.size());
            List<String> idPageList = idLists.subList(fromIndex, toIndex);
            objectDataList = customerBiz.getObjectByIds(tenantId, idPageList, LEADS_OBJ);
            log.info("recalculateByRule leadsPool loop tenantId:{} leadsPoolId:{},fromIndex:{},toIndex:{},idPageList:{}", tenantId, poolId, fromIndex, toIndex, idPageList);
            for (IObjectData leads : objectDataList) {
                if (Boolean.TRUE.equals(recyclingTaskGray.toSlowRecyclingTenantId(message.getTenantId()))) {
                    sfaRecyclingTaskRateLimiterService.getCrmSlowRecyclingLimiter().acquire();
                } else {
                    sfaRecyclingTaskRateLimiterService.getCrmRecalculateDeptLimiter().acquire();
                }

                // 检查线索的线索池ID是否与当前处理的线索池ID一致
                // 如果不一致，说明在计算过程中线索被移动到了其他线索池，需要获取新的回收规则
                String currentLeadsPoolId = leads.get(LEADS_POOL_ID, String.class);
                List<RecyclingRuleInfoModel> currentRecyclingRules = recyclingRules;

                if (!poolId.equals(currentLeadsPoolId)) {
                    log.info("LeadsObj leads_pool_id changed during calculation, leadsId:{}, originalPoolId:{}, currentPoolId:{}",
                            leads.getId(), poolId, currentLeadsPoolId);
                    // 重新获取当前线索池的回收规则
                    if (StringUtils.isNotBlank(currentLeadsPoolId)) {
                        currentRecyclingRules = recyclingBiz.getRecyclingRule(tenantId, currentLeadsPoolId, LEADS_POOL_OBJ);
                        log.info("Retrieved new recycling rules for poolId:{}, rulesCount:{}", currentLeadsPoolId,
                                CollectionUtils.isEmpty(currentRecyclingRules) ? 0 : currentRecyclingRules.size());
                    } else {
                        // 如果线索池ID为空，则使用空规则列表，这将在newRecalculateLeadsExtend中通过objectData重新获取
                        currentRecyclingRules = null;
                        log.info("LeadsObj leads_pool_id is empty for leadsId:{}, will use objectData to get rules", leads.getId());
                    }
                }

                newRecalculateLeadsExtend(tenantId, leads, currentRecyclingRules, null);
            }
        }
        log.info("LeadsObj recalculateByRule end,tenantId:{},leadsPoolId:{},idList:{} ", tenantId, poolId, idLists.size());
    }

    private void newRecalculateDefault(RecalculateMessage message) {
        IObjectData objectData = message.getObjectData();
        if (Boolean.TRUE.equals(worthCalculate(objectData))) {
            sfaRecyclingTaskRateLimiterService.getCrmRecalculateLimiter().acquire();
            newRecalculateLeadsExtend(message.getTenantId(), message.getObjectData(), null, message.getExtendDays());
        } else {
            log.info("leads clear actionCode:{},objectId:{},tenantId:{},lifeStatus:{},bizStatus:{}"
                    , message.getActionCode(), message.getObjectId(), message.getTenantId(), objectData.get(LIFE_STATUS), objectData.get(BIZ_STATUS));
            clear(objectData);
        }
    }


    public void newRecalculateLeadsExtend(String tenantId, IObjectData objectData, List<RecyclingRuleInfoModel> recyclingRules, Double extendDays) {
        sfaRecyclingTaskRateLimiterService.getCrmRecalculateLimiter().acquire();
        log.info("newRecalculateLeads 计算线索到期时间:{},objectId:{},extendDays:{}", tenantId, objectData.getId(), extendDays);
        if (CollectionUtils.isEmpty(recyclingRules)) {
            recyclingRules = recyclingBiz.getRecyclingRule(objectData, LEADS_POOL_OBJ);
            if (CollectionUtils.isEmpty(recyclingRules)) {
                log.info("recyclingRules is null clearExpireTime:{}", objectData.getId());
                leadsBiz.clearExpireTime(objectData);
                deleteRecycling(objectData.getTenantId(), objectData.getId());
            }
        }

        List<RuleTypeRemindTime> remindTimes = new ArrayList<>();
        RuleTypeRemindTime ruleTypeRemindTime = RuleTypeRemindTime.builder().build();

        Pair<Date, Date> datePair = calculateExpireTimeByNewRule(tenantId, ApiNameEnum.LEADS_OBJ.getApiName(), objectData, recyclingRules, ruleTypeRemindTime, extendDays);
        // 回收规则和提醒都为空  或者没有预计收回时间则返回
        if (datePair == null || datePair.getKey() == null) {
            delete(tenantId, objectData.getId());
            return;
        }
        Date remindTime = datePair.getValue();
        RemindMessage remindMessage = RemindMessage.builder()
                .tenantId(tenantId)
                .objectApiName(ApiNameEnum.LEADS_OBJ.getApiName())
                .objectId(objectData.getId())
                .build();
        if (remindTime != null) {
            ruleTypeRemindTime.setRemindTime(remindTime.getTime());
            remindTimes.add(ruleTypeRemindTime);

            remindMessage.setRemindTime(remindTime);
            remindMessage.setRuleTypeRemindTimes(remindTimes);
            sendRemindTask(remindMessage);
            log.info("leadsObj datePair {}, remindMessage:{},objectId:{}", datePair, remindMessage, objectData.getId());
        } else {
            deleteRemind(tenantId, objectData.getId());
        }
    }

    public Date getExpireTimeSkipHolidays(Date expireTIme, Integer ruleHours, IObjectData objectData) {
        Date minChangeTime = DateUtils.beforeHours(expireTIme, ruleHours);
        Long remainingDays = getLeadsRemainingDays(minChangeTime, ruleHours, objectData);
        return DateUtils.afterDays(expireTIme, remainingDays);
    }

    protected Long getLeadsRemainingDays(Date expireTime, Integer ruleHours, IObjectData objectData) {
        long extendHolidays = 0L;
        long holidays = 0L;
        // 计算横跨了几天
        long remainingDays = ruleHours / 24 + 1;
         // 小于24小时的回收时间，如果夸天,则是横跨了 2 天
        if (ruleHours < 24 && !DateUtils.isSameDay(expireTime, DateUtils.beforeHours(expireTime, ruleHours))){
            remainingDays = 2;
        }
        Date beginDate = expireTime;
        while (remainingDays > 0 && extendHolidays <= 1000) {
            long days = getDays((int) remainingDays);
            beginDate = DateUtils.afterDays(beginDate, extendHolidays);
            Date endDate = DateUtils.afterDays(beginDate, days);
            // 结束时间小于当前时间，窗口时间后移，直到结束时间大于当前时间
            if (endDate.before(new Date())) {
                //remainingDays 减去 days， 减去已经过去的天数
                remainingDays -= days;
                log.info("getRemainingDays id:{},beginDate:{},endDate:{},days:{}", objectData.getId(), beginDate, endDate, days);
                beginDate = endDate;
                continue;
            }
            log.info("getRemainingDays id:{},getRemoteParams: beginDate:{},endDate:{},days:{}", objectData.getId(), beginDate, endDate, days);
            List<String> dateListsStr = DateUtils.getBetweenDateListsStr(beginDate, endDate);
            List<com.fxiaoke.common.Pair<String, GetUserAttendanceInfos.AttendanceInfo>> pairs = getRemotePairs(beginDate, objectData, days);
            if (pairs == null) {
                return holidays;
            }
            log.info("id:{},expireTime:{},days:{}", objectData.getId(), expireTime, days);
            List<String> apiDateLists = pairs.stream().map(x -> x.first).collect(Collectors.toList());
            int index = 0;
            for (String dateStr : dateListsStr) {
                if (index >= apiDateLists.size()) {
                    break;
                }
                com.fxiaoke.common.Pair<String, GetUserAttendanceInfos.AttendanceInfo> pair = pairs.get(index);
                if (apiDateLists.contains(dateStr)) {
                    index++;
                }
                extendHolidays++;
                //节假日
                if (dateStr.equals(pair.first) && pair.second.getIsWork() == 0) {
                    holidays++;
                    //第一个工作日不扣减
                } else if (pair.second.getIsWork() == 1) {
                    remainingDays--;
                }
                if (remainingDays == 0L) {
                    break;
                }
            }
        }
        log.info("id:{},extendHolidays:{},holidays:{}", objectData.getId(), extendHolidays, holidays);
        return holidays;
    }
}
