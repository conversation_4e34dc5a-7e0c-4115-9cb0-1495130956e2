package com.facishare.crm.recycling

import com.facishare.crm.BaseGroovyTest
import com.facishare.crm.recycling.task.executor.biz.*
import com.facishare.crm.recycling.task.executor.common.RecyclingTaskGray
import com.facishare.crm.recycling.task.executor.model.RecyclingMessage
import com.facishare.crm.recycling.task.executor.service.SFAOpenApiMqService
import com.facishare.crm.recycling.task.executor.service.impl.AccountRecalculate
import com.facishare.crm.recycling.task.executor.service.impl.AccountRecycling
import com.facishare.paas.appframework.core.model.ServiceFacade
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.appframework.log.LogServiceImpl
import com.facishare.paas.appframework.metadata.MetaDataMiscService
import com.facishare.paas.appframework.metadata.MetaDataServiceImpl
import com.facishare.paas.metadata.api.service.IObjectDescribeService
import com.fxiaoke.paas.gnomon.api.NomonProducer
import com.google.common.collect.Lists
import org.junit.runner.RunWith
import org.powermock.api.mockito.PowerMockito
import org.powermock.core.classloader.annotations.PowerMockIgnore
import org.powermock.core.classloader.annotations.PrepareForTest
import org.powermock.modules.junit4.PowerMockRunner
import org.powermock.modules.junit4.PowerMockRunnerDelegate
import org.powermock.reflect.Whitebox
import org.spockframework.runtime.Sputnik

import static org.mockito.ArgumentMatchers.any

/**
 * <AUTHOR> lik
 * @date : 2024/2/20 14:45
 */
@RunWith(PowerMockRunner)
@PowerMockRunnerDelegate(Sputnik)
@PowerMockIgnore(["javax.management.*"])
@PrepareForTest([AccountRecycling.class, ServiceFacade.class, AccountRecalculate.class, CustomerBiz.class, HighSeasBiz.class, RemindBiz.class, RecyclingTaskGray.class, IObjectDescribeService.class, MetaDataMiscService.class, LogServiceImpl.class, RecyclingBiz.class, NomonProducer.class, MetaDataServiceImpl.class, SFAOpenApiMqService.class, ApprovalRemoteBiz.class])
class AccountRecyclingTest extends BaseGroovyTest {

    def setup() {
    }


    def "highSeasRecycling test"() {
        given:
        def mesg = new RecyclingMessage();
        mesg.setTenantId(tenantId)
        mesg.setObjectApiName(objectApiName)
        mesg.setObjectId(objectId)
        mesg.setTargetId(targetId)
        mesg.setFunctionApiName(functionApiName)
        mesg.setRecyclingReasonType(recyclingReasonType)
        mesg.setRecyclingDays(recyclingDays)
        mesg.setObjectData(objectData)
        def serviceFacade = PowerMockito.mock(ServiceFacade)
        def accountRecalculate = PowerMockito.mock(AccountRecalculate)
        def customerBiz = PowerMockito.mock(CustomerBiz)
        def highSeasBiz = PowerMockito.mock(HighSeasBiz)
        def remindBiz = PowerMockito.mock(RemindBiz)
        def recyclingTaskGray = PowerMockito.mock(RecyclingTaskGray)
        def objectDescribeService = PowerMockito.mock(IObjectDescribeService)
        def metaDataMiscService = PowerMockito.mock(MetaDataMiscService)
        def logService = PowerMockito.mock(LogServiceImpl)
        def recyclingBiz = PowerMockito.mock(RecyclingBiz)
        def nomonProducer = PowerMockito.mock(NomonProducer)
        def metaDataService = PowerMockito.mock(MetaDataServiceImpl)
        def sfaOpenApiMqService = PowerMockito.mock(SFAOpenApiMqService)
        def approvalRemoteBiz = PowerMockito.mock(ApprovalRemoteBiz)
        def accountRecycling = PowerMockito.spy(new AccountRecycling())
        Whitebox.setInternalState(accountRecycling, "serviceFacade", serviceFacade)
        Whitebox.setInternalState(accountRecycling, "accountRecalculate", accountRecalculate)
        Whitebox.setInternalState(accountRecycling, "customerBiz", customerBiz)
        Whitebox.setInternalState(accountRecycling, "highSeasBiz", highSeasBiz)
        Whitebox.setInternalState(accountRecycling, "remindBiz", remindBiz)
        Whitebox.setInternalState(accountRecycling, "objectDescribeService", objectDescribeService)
        Whitebox.setInternalState(accountRecycling, "metaDataMiscService", metaDataMiscService)
        Whitebox.setInternalState(accountRecycling, "logService", logService)
        Whitebox.setInternalState(accountRecycling, "recyclingBiz", recyclingBiz)
        Whitebox.setInternalState(accountRecycling, "nomonProducer", nomonProducer)
        Whitebox.setInternalState(accountRecycling, "metaDataService", metaDataService)
        Whitebox.setInternalState(accountRecycling, "sfaOpenApiMqService", sfaOpenApiMqService)
        Whitebox.setInternalState(accountRecycling, "approvalRemoteBiz", approvalRemoteBiz)
        Whitebox.setInternalState(accountRecycling, "recyclingTaskGray", recyclingTaskGray)
        PowerMockito.doReturn(objectData).when(serviceFacade, "findObjectData", new User(tenantId, User.SUPPER_ADMIN_USER_ID),objectId,objectApiName)
        PowerMockito.doNothing().when(accountRecalculate, "sendRecalculate", any(), any(), any(), any())
        PowerMockito.doNothing().when(customerBiz, "clearExpireTime", any())
        PowerMockito.doReturn(highSeasObjData).when(highSeasBiz, "getHighSeasById", any(), any())
        PowerMockito.doNothing().when(remindBiz, "sendRemind",  any(), any(), any(), any())
        PowerMockito.doReturn(highSeasObjData).when(highSeasBiz, "getHighSeasById", any(), any())
        PowerMockito.doReturn(true).when(recyclingTaskGray, "isExpireTimeExtend", any())
        PowerMockito.doReturn(null).when(objectDescribeService, "findByTenantIdAndDescribeApiName", any(), any())
        PowerMockito.doNothing().when(customerBiz, "updateFieldForRecycling", any(), any(), any(), any())
        PowerMockito.doNothing().when(metaDataMiscService, "fillUserInfo", any(), any(), any())
        PowerMockito.doReturn(new User(tenantId, userId)).when(customerBiz, "buildUser", any())
        PowerMockito.doNothing().when(logService, "logWithCustomMessage", any(), any(), any(),any(), any(), any())
        PowerMockito.doNothing().when(recyclingBiz, "addPoolClaimLog", any(), any(), any())
        PowerMockito.doReturn(null).when(customerBiz, "getContact", any(), any())
        PowerMockito.doNothing().when(customerBiz, "removeInnerRelevantTeam", any(), any() )
        PowerMockito.doNothing().when(customerBiz, "objectOutTeamHandle", any(), any(), any() )
        PowerMockito.doNothing().when(nomonProducer, "send", any() )
        PowerMockito.doReturn(objectData).when(customerBiz, "getObjectById", any(), any(), any())
        PowerMockito.doNothing().when(metaDataService, "sendActionMq", any(), any(), any() )
        PowerMockito.doNothing().when(sfaOpenApiMqService, "sendOpenApiMq", any(), any(), any(), any(), any() )
        PowerMockito.doReturn(null).when(serviceFacade, "findUDefFunction",  any(), any(), any())
        PowerMockito.doReturn(null).when(approvalRemoteBiz, "getCurInstanceStateByObjectIds",  any(), any())
        when:
        accountRecycling.highSeasRecycling(mesg)
        throw new OK()

        then:
        thrown(exception2)

        where:
        tenantId | objectApiName | objectId                   | targetId                   | objectData            | functionApiName    | recyclingReasonType | recyclingDays|exception2|highSeasObjData
        "79337"  | "AccountObj"  | "62fc5e5ef969e10001c16af5" | "6230001c9489770001c095dd" | getObjectData(Lists.newArrayList()) | null | 1 | 1 | OK|null
        "79337"  | "AccountObj"  | "62fc5e5ef969e10001c16af5" | "6230001c9489770001c095dd" | getObjectData(Lists.newArrayList("expire_time"),Lists.newArrayList("*************")) | null | 1 | 1 | OK|null
        "79337"  | "AccountObj"  | "62fc5e5ef969e10001c16af5" | "6230001c9489770001c095dd" | getObjectData(Lists.newArrayList("expire_time","life_status"),Lists.newArrayList("*************","invalid")) | null | 1 | 1 | OK|null
        "79337"  | "AccountObj"  | "62fc5e5ef969e10001c16af5" | "6230001c9489770001c095dd" | getObjectData(Lists.newArrayList("expire_time","life_status"),Lists.newArrayList("*************","normal")) | null | 1 | 1 | OK|null
        "79337"  | "AccountObj"  | "62fc5e5ef969e10001c16af5" | "6230001c9489770001c095dd" | getObjectData(Lists.newArrayList("name","expire_time","life_status","owner","high_seas_id","owner__r"),Lists.newArrayList("dewdewde","*************","normal",["1000"],"6230001c9489770001c095dd",getObjectData(Lists.newArrayList("name")))) | "212121" | 1 | 1 | OK|getObjectData(Lists.newArrayList("name","is_recycling_team_member"),Lists.newArrayList("name",true))

    }

}
