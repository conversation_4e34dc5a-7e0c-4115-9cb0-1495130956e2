package com.facishare.crm.sfa.expression;

import com.facishare.crm.sfa.expression.util.ConvertUtils;
import com.facishare.crm.sfa.expression.util.Pair;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.expression.ExpressionService;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.Wheres;
import com.fxiaoke.release.FsGrayRelease;
import com.fxiaoke.release.FsGrayReleaseBiz;
import com.google.common.collect.Lists;
import groovy.lang.MissingPropertyException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Date 2019/11/7 17:50
 */
@Component
@Slf4j
public class SFAExpressionServiceImpl implements SFAExpressionService {
    @Autowired
    private ExpressionService expressionService;

    private final FsGrayReleaseBiz grayReleaseBiz = FsGrayRelease.getInstance("sfa");

    private static final String NEVER_EQUAL_EXPRESSION = " (1 < 1) ";

    @Override
    public Boolean evaluate(String wheres, IObjectData objectData, IObjectDescribe objectDescribe) {
        List<Wheres> wheresList = ConvertUtils.convert2Wheres(wheres);
        List<IObjectData> objectDataList = ObjectDataExt.copyList(Lists.newArrayList(objectData));
        ConvertUtils.convertData(ConvertUtils.convert2Wheres(wheres), objectDataList, objectDescribe);
        String expression = buildExpressionString(wheresList, objectDescribe);
        Boolean evaluate;
        try {
            evaluate = expressionService.evaluate(expression, ObjectDataExt.toMap(objectDataList.get(0)));
            if (enabledLog(objectData.getTenantId())){
                printLog(ConvertUtils.convert2Wheres(wheres),objectDataList,objectDescribe,expression,evaluate);
            }
        } catch (MissingPropertyException e) {
            throw new MissingPropertyException("MissingProperty,expression:"+expression+",tenantId:"+
                    objectData.getTenantId()+", objectId:"+objectData.getId(), e.getType(), e);
        }
        return evaluate == null ? Boolean.FALSE : evaluate;
    }

    @Override
    public Map<String, Boolean> evaluate(String wheres, List<IObjectData> objectDataList, IObjectDescribe objectDescribe) {
        List<Wheres> wheresList = ConvertUtils.convert2WheresList(wheres);
        Map<String, Boolean> map = new HashMap<>();
        ConvertUtils.convertData(wheresList, objectDataList, objectDescribe);
        String expression = buildExpressionString(wheresList, objectDescribe);
        for (IObjectData objectData : objectDataList) {
            Boolean ret = expressionService.evaluate(expression, ObjectDataExt.toMap(objectData));
            map.put(objectData.getId(),ret);
        }
        return map;
    }

    @Override
    public Pair<String, IObjectData> getExpression(String wheres, IObjectData objectData, IObjectDescribe objectDescribe) {
        List<Wheres> wheresList = ConvertUtils.convert2WheresList(wheres);
        return new Pair<>(buildExpressionString(wheresList, objectDescribe), objectData);
    }

    private String buildExpressionString(List<Wheres> wheresList, IObjectDescribe objectDescribe) {
        List<String> expressionList = new ArrayList<>();
        if (CollectionUtils.isEmpty(wheresList)) {
            expressionList.add(" ( 1 == 1) ");
        } else {
            for (Wheres wheres : wheresList) {
                if (CollectionUtils.isEmpty(wheres.getFilters())){
                    expressionList.add(" ( 1 == 1) ");
                    continue;
                }
                List<String> tempExpressionList = Lists.newArrayList();
                for (IFilter filter : wheres.getFilters()) {
                    String expression = buildSingleExpressionString(filter, objectDescribe);
                    tempExpressionList.add(expression);
                }
                String wheresExpression = String.join(" && ", tempExpressionList);
                wheresExpression = String.format(" ( %s ) ", wheresExpression);
                expressionList.add(wheresExpression);
            }
        }
        String result = String.join(" || ", expressionList);
        if (StringUtils.isBlank(result)) {
            result = " ( 1 == 1) ";
        }
        return result;
    }

    private String buildSingleExpressionString(IFilter filter, IObjectDescribe objectDescribe) {
        List<IFieldDescribe> collect = objectDescribe.getFieldDescribes().stream().filter(
                x -> x.getApiName().equals(filter.getFieldName())).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(collect)) {
            return NEVER_EQUAL_EXPRESSION;
        }
        String type = collect.get(0).getType();

        switch (type) {
            case IFieldType.CURRENCY:
            case IFieldType.NUMBER:
			case IFieldType.PERCENTILE:
                return getNumberExpression(filter);
            case IFieldType.DATE:
            case IFieldType.DATE_TIME:
                return getDateExpression(filter);
            case IFieldType.RECORD_TYPE:
            case IFieldType.SELECT_ONE:
            case IFieldType.COUNTRY:
            case IFieldType.PROVINCE:
            case IFieldType.CITY:
            case IFieldType.DISTRICT:
                return getSelectOneExpression(filter);
            case IFieldType.SELECT_MANY:
                return getSelectMany(filter);
//            case IFieldType.QUOTE:
//                formatQuote(filter, objectDescribe, collect);
            case IFieldType.OBJECT_REFERENCE:
                filter.setFieldName(String.format("%s__r", filter.getFieldName()));
                return getTextExpression(filter);
            case IFieldType.EMPLOYEE:
            case IFieldType.DEPARTMENT:
                return getOrgExpression(filter);
            default:
                return getTextExpression(filter);
        }
    }

    // 金额、整数
    // 等于、不等于、小于、大于、大于等于、小于等于
    private static String getNumberExpression(IFilter filter) {
        if (CollectionUtils.isEmpty(filter.getFieldValues())) {
            return NEVER_EQUAL_EXPRESSION;
        }
        String fieldValue = filter.getFieldValues().get(0);

        String rst;
        switch (filter.getOperator()) {
            case EQ:
                rst = String.format(" (%s == %s) ", filter.getFieldName(), fieldValue);
                break;
            case N:
            case NEQ:
                rst = String.format(" NOT(%s == %s) ", filter.getFieldName(), fieldValue);
                break;
            case LT:
                rst = String.format(" (%s < %s) ", filter.getFieldName(), fieldValue);
                rst += String.format(" && !ISNULL(%s) ",filter.getFieldName());
                break;
            case LTE:
                rst = String.format(" (%s <= %s) ", filter.getFieldName(), fieldValue);
                rst += String.format(" && !ISNULL(%s) ",filter.getFieldName());
                break;
            case GT:
                rst = String.format(" (%s > %s) ", filter.getFieldName(), fieldValue);
                rst += String.format(" && !ISNULL(%s) ",filter.getFieldName());
                break;
            case GTE:
                rst = String.format(" (%s >= %s) ", filter.getFieldName(), fieldValue);
                rst += String.format(" && !ISNULL(%s) ",filter.getFieldName());
                break;
            default:
                rst = NEVER_EQUAL_EXPRESSION;
                break;
        }
        return rst;
    }

    // 日期
    // 等于、早于、晚于、自定义
    private static String getDateExpression(IFilter filter) {
        long time;
        long time2;
        String fieldValue = "";
        if (!CollectionUtils.isEmpty(filter.getFieldValues())) {
            fieldValue = filter.getFieldValues().get(0);
        }
        String rst;
        switch (filter.getOperator()) {
            case EQ:
                if (CollectionUtils.isEmpty(filter.getFieldValues())) {
                    rst = NEVER_EQUAL_EXPRESSION;
                    break;
                }
                time = Long.parseLong(fieldValue);
                rst = String.format(" (%s == %d) ", filter.getFieldName(),
                        time);
                break;
            case N:
            case NEQ:
                if (CollectionUtils.isEmpty(filter.getFieldValues())) {
                    rst = NEVER_EQUAL_EXPRESSION;
                    break;
                }
                time = Long.parseLong(fieldValue);
                rst = String.format("  NOT(%s == %d) ", filter.getFieldName(),
                        time);
                break;
            case LT:
                if (CollectionUtils.isEmpty(filter.getFieldValues())) {
                    rst = NEVER_EQUAL_EXPRESSION;
                    break;
                }
                time = Long.parseLong(fieldValue);

                rst = String.format(" (%s < %d)  ", filter.getFieldName(), time);
                rst += String.format(" && !ISNULL(%s) ",filter.getFieldName());
                break;
            case GT:
                if (CollectionUtils.isEmpty(filter.getFieldValues())) {
                    rst = NEVER_EQUAL_EXPRESSION;
                    break;
                }
                time = Long.parseLong(fieldValue);
                rst = String.format(" (%s > %d) ", filter.getFieldName(), time);
                break;
            case BETWEEN:
                if (CollectionUtils.isEmpty(filter.getFieldValues())) {
                    rst = NEVER_EQUAL_EXPRESSION;
                    break;
                }
                time = Long.parseLong(fieldValue);
                time2 = Long.parseLong(filter.getFieldValues().get(1));
                rst = String.format(" (%s >= %d)  && (%s <= %d)", filter.getFieldName(),
                        time, filter.getFieldName(), time2 + 24 * 60 * 60 * 1000 - 1);
                break;
            case IS:
                rst = String.format(" ISNULL(%s) ", filter.getFieldName());
                break;
            case ISN:
                rst = String.format(" !ISNULL(%s) ", filter.getFieldName());
                break;
            default:
                rst = NEVER_EQUAL_EXPRESSION;
                break;
        }
        return rst;
    }

    // 单选
    //
    private static String getSelectOneExpression(IFilter filter) {
        String rst;
        switch (filter.getOperator()) {
            case EQ:
            case IN:
            case LIKE:
            case HASANYOF:
                if (CollectionUtils.isEmpty(filter.getFieldValues())) {
                    rst = NEVER_EQUAL_EXPRESSION;
                    break;
                }
                StringBuilder sb = new StringBuilder(" ISSelectOptionVAL(" + filter.getFieldName());
                for (int i = 0; i < filter.getFieldValues().size(); i++) {
                    sb.append(",'" + filter.getFieldValues().get(i) + "'");
                }
                sb.append(") ");
                rst = sb.toString();
                break;
            case N:
            case NEQ:
            case NIN:
            case NLIKE:
            case NHASANYOF:
                if (CollectionUtils.isEmpty(filter.getFieldValues())) {
                    rst = NEVER_EQUAL_EXPRESSION;
                    break;
                }
                sb = new StringBuilder(" !ISSelectOptionVAL(" + filter.getFieldName());
                for (int i = 0; i < filter.getFieldValues().size(); i++) {
                    sb.append(",'" + filter.getFieldValues().get(i) + "'");
                }
                sb.append(") ");
                rst = sb.toString();
                break;
            case IS:
                rst = String.format(" ISNULL(%s) ", filter.getFieldName());
                break;
            case ISN:
                rst = String.format(" !ISNULL(%s) ", filter.getFieldName());
                break;
            default:
                rst = NEVER_EQUAL_EXPRESSION;
                break;
        }
        return rst;
    }

    // 多选
    private static String getSelectMany(IFilter filter) {
        String rst;
        StringBuilder sb;
        switch (filter.getOperator()) {
            case EQ:
                if (CollectionUtils.isEmpty(filter.getFieldValues())) {
                    rst = NEVER_EQUAL_EXPRESSION;
                    break;
                }
                sb = new StringBuilder(" (" + filter.getFieldName() + " == [");
                Collections.sort(filter.getFieldValues());
                sb.append("'" + filter.getFieldValues().get(0) + "'");
                for (int i = 1; i < filter.getFieldValues().size(); i++) {
                    sb.append(",'" + filter.getFieldValues().get(i) + "'");
                }
                sb.append("]) ");
                rst = sb.toString();
                break;
            case N:
            case NEQ:
                if (CollectionUtils.isEmpty(filter.getFieldValues())) {
                    rst = NEVER_EQUAL_EXPRESSION;
                    break;
                }
                sb = new StringBuilder(" (" + filter.getFieldName() + " != [");
                Collections.sort(filter.getFieldValues());
                sb.append("'" + filter.getFieldValues().get(0) + "'");
                for (int i = 1; i < filter.getFieldValues().size(); i++) {
                    sb.append(",'" + filter.getFieldValues().get(i) + "'");
                }
                sb.append("]) ");
                rst = sb.toString();
                break;
            case IN:
            case HASANYOF:
                if (CollectionUtils.isEmpty(filter.getFieldValues())) {
                    rst = NEVER_EQUAL_EXPRESSION;
                    break;
                }
                sb = new StringBuilder(" INCLUDES(" + filter.getFieldName() + " ");
                for (int i = 0; i < filter.getFieldValues().size(); i++) {
                    sb.append(",'" + filter.getFieldValues().get(i) + "'");
                }
                sb.append(") ");
                rst = sb.toString();
                break;
            case NIN:
            case NHASANYOF:
                if (CollectionUtils.isEmpty(filter.getFieldValues())) {
                    rst = NEVER_EQUAL_EXPRESSION;
                    break;
                }
                sb = new StringBuilder(" !INCLUDES(" + filter.getFieldName() + " ");
                for (int i = 0; i < filter.getFieldValues().size(); i++) {
                    sb.append(",'" + filter.getFieldValues().get(i) + "'");
                }
                sb.append(") ");
                rst = sb.toString();
                break;
            case IS:
                rst = String.format(" ISNULL(%s) ", filter.getFieldName());
                break;
            case ISN:
                rst = String.format(" !ISNULL(%s) ", filter.getFieldName());
                break;
            default:
                rst = NEVER_EQUAL_EXPRESSION;
                break;
        }
        return rst;
    }

    // 人员、部门
    private static String getOrgExpression(IFilter filter) {
        String rst;
        String filedName = filter.getFieldName();
        String fieldValue = "";
        if (!CollectionUtils.isEmpty(filter.getFieldValues())) {
            fieldValue = filter.getFieldValues().get(0);
        }

        switch (filter.getOperator()) {
            case EQ:
                rst = String.format(" EQUALS(%s,',%s,') ", filedName, fieldValue);
                break;
            case N:
            case NEQ:
                rst = String.format(" !EQUALS(%s,',%s,') ", filedName, fieldValue);
                break;
            case IN:
            case LIKE:
                if (CollectionUtils.isEmpty(filter.getFieldValues())) {
                    rst = NEVER_EQUAL_EXPRESSION;
                    break;
                }
                fieldValue = String.join(",", filter.getFieldValues());
                fieldValue = String.format(",%s,", fieldValue);
                rst = String.format(" (!ISNULL(%s) && CONTAINS('%s',%s))", filedName,fieldValue, filedName);
                break;
            case NIN:
            case NLIKE:
                if (CollectionUtils.isEmpty(filter.getFieldValues())) {
                    rst = NEVER_EQUAL_EXPRESSION;
                    break;
                }
                fieldValue = String.join(",", filter.getFieldValues());
                fieldValue = String.format(",%s,", fieldValue);
                rst = String.format(" !(!ISNULL(%s) && CONTAINS('%s',%s))",filedName,fieldValue, filedName);
                break;
            case IS:
                rst = String.format(" ISNULL(%s) ", filter.getFieldName());
                break;
            case ISN:
                rst = String.format(" !ISNULL(%s) ", filter.getFieldName());
                break;
            default:
                rst = NEVER_EQUAL_EXPRESSION;
                break;
        }
        return rst;
    }

    // 等于、不等于、包含、不包含、为空、不为空
    private static String getTextExpression(IFilter filter) {
        String rst;
        String fieldName = filter.getFieldName();
        String fieldValue = "";
        if (!CollectionUtils.isEmpty(filter.getFieldValues())) {
            fieldValue = filter.getFieldValues().get(0);
        }

        if (StringUtils.isNotBlank(fieldValue) && fieldValue.contains("'")) {
            fieldValue = fieldValue.replaceAll("'", "\\\\'");
        }
        switch (filter.getOperator()) {
            case EQ:
                rst = String.format(" EQUALS(%s,'%s') ", fieldName, fieldValue);
                break;
            case N:
            case NEQ:
                rst = String.format(" !EQUALS(%s,'%s') ", fieldName, fieldValue);
                break;
            case IN:
            case LIKE:
                rst = String.format(" CONTAINS(%s,'%s')", fieldName, fieldValue);
                break;
            case NIN:
            case NLIKE:
                rst = String.format(" !CONTAINS(%s,'%s') ", fieldName, fieldValue);
                break;
            case IS:
                rst = String.format(" ISNULL(%s) ", fieldName);
                break;
            case ISN:
                rst = String.format(" !ISNULL(%s) ", fieldName);
                break;
            case STARTWITH:
                rst = String.format(" STARTWITH(%s,'%s') ", fieldName, fieldValue);
                break;
            case ENDWITH:
                rst = String.format(" ENDWITH(%s,'%s') ", fieldName, fieldValue);
                break;
            default:
                rst = NEVER_EQUAL_EXPRESSION;
                break;
        }
        return rst;
    }


    public boolean enabledLog(String tenantId){
        return grayReleaseBiz.isAllow("enabled_expression_log",tenantId);
    }


    private void printLog(List<Wheres> wheresList, List<IObjectData> objectDataList, IObjectDescribe objectDescribe,String expression,Boolean evaluate){
        List<Map<String,Object>> dataMaps = new ArrayList<>();
        for (Wheres wheres : wheresList) {
            Set<String> fieldList = wheres.getFilters().stream().map(IFilter::getFieldName).collect(Collectors.toSet());

            List<IFieldDescribe> fieldDescribes = objectDescribe.getFieldDescribes().stream()
                    .filter(x -> fieldList.contains(x.getApiName())).collect(Collectors.toList());
            if(CollectionUtils.isEmpty(fieldDescribes)) {
                continue;
            }

            for(IObjectData objectData : objectDataList) {
                Map<String,Object> map = new HashMap<>();
                for(IFieldDescribe fieldDescribe : fieldDescribes) {
                    Object o;
                    if(fieldDescribe.getType().equals(IFieldType.OBJECT_REFERENCE)) {
                        String referenceNameApi = String.format("%s__r", fieldDescribe.getApiName());
                        o = objectData.get(referenceNameApi);
                    }else {
                        o = objectData.get(fieldDescribe.getApiName());
                    }
                    map.put(fieldDescribe.getApiName(),o);
                }
                map.put("id",objectData.getId());
                dataMaps.add(map);
            }
        }
        log.info("expression:{}; objectData:{},evaluate:{}",expression,dataMaps,evaluate);
    }

}
