package com.facishare.crm.sfa.lto.loyalty.model;

import com.facishare.paas.metadata.api.IObjectData;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public interface Loyalty {

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class PointsOperationParam {
        private String tenantId;
        /**
         * 唯一id，同一个id的操作不会重复执行，用于幂等
         */
        private String uniqueId;
        /**
         * 会员id
         */
        private String memberId;
        /**
         * 用户行为，由用户自己填写的描述
         */
        private String desc;
        /**
         * 操作类型
         * CONSUMER_POINTS_TO_MEMBER 为给员工加积分
         * CONSUMER_POINTS_TO_POOL 为给员工扣积分
         */
        private Type type;
        /**
         * 积分变动值。消费积分增减都必须填
         */
        private Long value;
        /**
         * 积分池id。消费积分增减都必须填
         */
        private String pointPoolId;
        /**
         * 积分分类，定级积分变动时，需要指定积分分类
         */
        private String pointTypeId;
        /**
         * 直接修改等级时必填
         */
        private String tierId;

        /**
         * 转移积分时必填 type=TRANSFER_POINTS
         * 积分来源的会员id
         */
        private String transferMemberId;

        public enum Type {
            /**
             * 给会员加积分
             */
            CONSUMER_POINTS_TO_MEMBER,
            /**
             * 扣除会员积分
             */
            CONSUMER_POINTS_TO_POOL,
            /**
             * 会员定级积分增/减
             */
            LEVEL_POINTS,
            /**
             * 设定会员等级
             */
            SET_LEVEL,
            /**
             * 动态设定会员等级
             */
            CHANGE_LEVEL,
            /**
             * 转移消费积分
             */
            TRANSFER_CONSUMER_POINTS,
            /**
             * 转移定级积分
             */
            TRANSFER_LEVEL_POINTS,
            /**
             * 转移冻结消费积分
             */
            TRANSFER_FREEZE_CONSUMER_POINTS,
            /**
             * 转移冻结定级积分
             */
            TRANSFER_FREEZE_LEVEL_POINTS,
        }

        //以下为追加参数,不必填
        private String programId;
        /**
         * 操作人id，通过页面按钮操作
         */
        private String operator;
        /**
         * 激励政策
         */
        private String incentivePolicyId;
        /**
         * 激励规则
         */
        private String incentivePolicyRuleId;
        /**
         * 交易事件
         */
        private String transactionEventId;
        /**
         * 来源对象apiName
         */
        private String sourceApiName;
        /**
         * 来源对象dataId
         */
        private String sourceId;
        /**
         * 上下文，多次进行该接口操作时，用于缓存部分数据
         */
        private Map<String, Object> context;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class FallbackParam {
        private String tenantId;
        private String memberId;
        private String uniqueId;
        private List<String> dataIdList;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    class FallbackInfo {
        private IObjectData record;
        private IObjectData pointsDetail;
        /**
         * 转入会员积分明细
         */
        private IObjectData transferIntoPointsDetail;
        /**
         * 转出会员积分明细
         */
        private IObjectData transferOutPointsDetail;
    }

    @Data
    class FallbackUpdateData {
        List<IObjectData> updatePointsDetailList = new ArrayList<>();
    }

    @Data
    class MemberTask {
        private String tenantId;
        private String traceId;
        private String memberId;
        private String operator;
    }


    @Data
    class OrgTask {
        private String tenantId;
        private String poolId;
    }

    @Data
    class MemberUpgrade {
        private String tenantId;
        private String memberId;
        private String beforeTierId;
        private String afterTierId;
    }

    @Data
    class MemberEvaluationDate {
        private String tenantId;
        private List<String> memberIdList;
    }

    @Data
    class MemberMerge {
        private String tenantId;
        private List<String> souceIdList;
        private String targetId;
        private Long startTime;
        private Long endTime;

        private String operator;
        private String traceId;
        //以下参数用于记录执行偏移量
        /**
         * 用来记录当前任务执行序列
         * 例如：
         * 1.合并积分明细
         * 2.合并会员变动记录
         */
        private Integer no;
        /**
         * 记录当前执行id
         * 使用该字段进行偏移量查询
         * 为空时表示从头开始查询
         * 查询不到数据时表示当前任务执行完毕，no+1
         */
        private String offset;
    }

}