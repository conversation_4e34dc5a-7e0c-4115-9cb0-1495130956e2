package com.facishare.crm.sfa.lto.loyalty.service;

import com.facishare.crm.sfa.lto.loyalty.constants.LoyaltyConstants;
import com.facishare.crm.sfa.lto.loyalty.i18n.LoyaltyI18nException;
import com.facishare.crm.sfa.lto.loyalty.service.memberTierStrategy.IMemberTierStrategy;
import com.facishare.crm.sfa.lto.loyalty.utils.LoyaltyI18nKey;
import com.facishare.crm.sfa.lto.utils.SearchUtil;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Slf4j
@Service
public class LoyaltyMemberTierService {

    @Resource
    private ServiceFacade serviceFacade;
    @Resource
    private List<IMemberTierStrategy> strategyList;

    /**
     * @return 需要更新的字段值，不会自动更新
     */
    public Map<String, Object> run(String tenantId, IObjectData member, IMemberTierStrategy.Operator operator) {
        //当前会员等级
        IObjectData currentTier = serviceFacade.findObjectDataIgnoreAll(User.systemUser(tenantId), member.get(LoyaltyConstants.LoyaltyMember.TIER_ID, String.class), LoyaltyConstants.LoyaltyTier.API_NAME);
        //当前会员等级分类id
        String oldTierClassInfoId = currentTier.get(LoyaltyConstants.LoyaltyTier.TIER_CLASS_ID, String.class);
        //该会员计划下正在生效的等级分类
        IObjectData tierClassInfo = getEffectiveTierClassByProgramId(tenantId, member.get(LoyaltyConstants.LoyaltyMember.PROGRAM_ID, String.class));
        if (tierClassInfo == null) {
            throw LoyaltyI18nException.build(LoyaltyI18nKey.NOT_FOUND, LoyaltyConstants.LoyaltyTierClass.API_NAME);
        }
        IMemberTierStrategy.Param param = new IMemberTierStrategy.Param(member, tierClassInfo, currentTier);
        param.setNotChangeTierClass(Objects.equals(oldTierClassInfoId, tierClassInfo.getId()));
        for (IMemberTierStrategy strategy : strategyList) {
            if (strategy.name().equals(tierClassInfo.get(LoyaltyConstants.LoyaltyTierClass.LEVEL_MODE, String.class))) {
                switch (operator) {
                    case upgrades: {
                        return strategy.upgrades(tenantId, param);
                    }
                    case task: {
                        return strategy.task(tenantId, param);
                    }
                }
            }
        }
        return new HashMap<>();
    }


    /**
     * 查询会员计划下有效id
     */
    public IObjectData getEffectiveTierClassByProgramId(String tenantId, String programId) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, IObjectData.IS_DELETED, 0);
        SearchUtil.fillFilterEq(filters, LoyaltyConstants.LoyaltyTierClass.PROGRAM_ID, programId);
        SearchUtil.fillFilterEq(filters, LoyaltyConstants.LoyaltyTierClass.TIER_CLASS_STATUS, "Valid");
        query.setFilters(filters);
        query.setLimit(1);
        query.setOrders(
                Lists.newArrayList(
                        new OrderBy(LoyaltyConstants.LoyaltyTierClass.SEQ, true),
                        new OrderBy(IObjectData.CREATE_TIME, true)
                )
        );
        QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQuery(User.systemUser(tenantId), LoyaltyConstants.LoyaltyTierClass.API_NAME, query);
        if (queryResult == null || CollectionUtils.isEmpty(queryResult.getData())) {
            return null;
        }
        return queryResult.getData().get(0);
    }
}
