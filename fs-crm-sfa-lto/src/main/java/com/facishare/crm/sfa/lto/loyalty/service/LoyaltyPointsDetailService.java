package com.facishare.crm.sfa.lto.loyalty.service;

import com.facishare.crm.sfa.lto.loyalty.constants.LoyaltyConstants;
import com.facishare.crm.sfa.lto.loyalty.model.Loyalty;
import com.facishare.crm.sfa.lto.loyalty.utils.LoyaltyThreshold;
import com.facishare.crm.sfa.lto.loyalty.utils.LoyaltyUtils;
import com.facishare.crm.sfa.lto.loyalty.utils.NumberUtils;
import com.facishare.crm.sfa.lto.utils.SearchUtil;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.Count;
import com.facishare.paas.metadata.api.search.AggFunctionArg;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.IGroupByParameter;
import com.facishare.paas.metadata.impl.search.GroupByParameter;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.fxiaoke.api.IdGenerator;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class LoyaltyPointsDetailService {

    @Resource
    private ServiceFacade serviceFacade;

    /**
     * 根据积分明细统计回填会员积分
     * 返回更新的字段map
     */
    public Map<String, Object> fillPoints(String tenantId, String memberId) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, LoyaltyConstants.LoyaltyPointsDetail.MEMBER_ID, memberId);
        SearchUtil.fillFilterIn(filters, LoyaltyConstants.LoyaltyPointsDetail.POINTS_STATUS, Lists.newArrayList("Available", "Frozen"));
        query.setFilters(filters);
        AggFunctionArg aggFunctionArg = AggFunctionArg.builder()
                .aggFunction(Count.TYPE_SUM)
                .aggField(LoyaltyConstants.LoyaltyPointsDetail.AVAILABLE_POINTS)
                .build();
        IGroupByParameter groupByParameter = new GroupByParameter();
        groupByParameter.setAggFunctions(Lists.newArrayList(aggFunctionArg));
        groupByParameter.setGroupBy(Lists.newArrayList(LoyaltyConstants.LoyaltyPointsDetail.MEMBER_ID, LoyaltyConstants.LoyaltyPointsDetail.POINTS_STATUS, LoyaltyConstants.LoyaltyPointsDetail.IS_QUALIFYING));
        query.setGroupByParameter(groupByParameter);
        List<IObjectData> res = serviceFacade.aggregateFindBySearchQuery(User.systemUser(tenantId), query, LoyaltyConstants.LoyaltyPointsDetail.API_NAME);
        //返回结果
        Map<String, Object> updateFields = new HashMap<>();
        updateFields.put(LoyaltyConstants.LoyaltyMember.GRADING_POINTS, 0);
        updateFields.put(LoyaltyConstants.LoyaltyMember.CONSUMER_POINTS, 0);
        updateFields.put(LoyaltyConstants.LoyaltyMember.FROZEN_GRADING_POINTS, 0);
        updateFields.put(LoyaltyConstants.LoyaltyMember.FROZEN_POINTS, 0);
        for (IObjectData data : res) {
            if ("Available".equals(data.get(LoyaltyConstants.LoyaltyPointsDetail.POINTS_STATUS, String.class))) {
                if (Boolean.TRUE.equals(data.get(LoyaltyConstants.LoyaltyPointsDetail.IS_QUALIFYING, Boolean.class))) {
                    updateFields.put(LoyaltyConstants.LoyaltyMember.GRADING_POINTS, NumberUtils.getLong(data, "sum_available_points"));
                } else {
                    updateFields.put(LoyaltyConstants.LoyaltyMember.CONSUMER_POINTS, NumberUtils.getLong(data, "sum_available_points"));
                }
            } else if ("Frozen".equals(data.get(LoyaltyConstants.LoyaltyPointsDetail.POINTS_STATUS, String.class))) {
                if (Boolean.TRUE.equals(data.get(LoyaltyConstants.LoyaltyPointsDetail.IS_QUALIFYING, Boolean.class))) {
                    updateFields.put(LoyaltyConstants.LoyaltyMember.FROZEN_GRADING_POINTS, NumberUtils.getLong(data, "sum_available_points"));
                } else {
                    updateFields.put(LoyaltyConstants.LoyaltyMember.FROZEN_POINTS, NumberUtils.getLong(data, "sum_available_points"));
                }
            }
        }
        return updateFields;
    }

    /**
     * 查询员工可用的的全部积分详情
     */
    public List<IObjectData> findMemberPointsDetails(String tenantId, String memberId, boolean isQualifying) {
        List<IFilter> filterList = Lists.newArrayList();
        IFilter iFilter = SearchUtil.filter(LoyaltyConstants.LoyaltyPointsDetail.IS_QUALIFYING, com.facishare.paas.metadata.impl.search.Operator.EQ, isQualifying);
        iFilter.setIsMasterField(true);
        filterList.add(iFilter);
        SearchUtil.fillFilterEq(filterList, LoyaltyConstants.LoyaltyPointsDetail.POINTS_STATUS, "Available");
        return findMemberPointsDetails(tenantId, memberId, filterList);
    }

    public List<IObjectData> findMemberPointsDetails(String tenantId, String memberId, List<IFilter> filterList) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, LoyaltyConstants.LoyaltyPointsDetail.MEMBER_ID, memberId);
        SearchUtil.fillFilterGT(filters, LoyaltyConstants.LoyaltyPointsDetail.AVAILABLE_POINTS, 0L);
        filters.addAll(filterList);
        query.setFilters(filters);
        query.setLimit(LoyaltyThreshold.getPointsDetailsLimit());
        query.setOrders(Lists.newArrayList(new OrderBy(LoyaltyConstants.LoyaltyPointsDetail.EXPIRY_TIME, true), new OrderBy(IObjectData.CREATE_TIME, true)));
        QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQuery(User.systemUser(tenantId), LoyaltyConstants.LoyaltyPointsDetail.API_NAME, query);
        if (queryResult == null || CollectionUtils.isEmpty(queryResult.getData())) {
            return Lists.newArrayList();
        }
        return queryResult.getData();
    }

    /**
     * 插入积分明细
     */
    public ObjectDataDocument saveLoyaltyPointsDetail(Loyalty.PointsOperationParam param) {
        String tenantId = param.getTenantId();
        IObjectData pointType = serviceFacade.findObjectDataIgnoreAll(User.systemUser(tenantId), param.getPointTypeId(), LoyaltyConstants.LoyaltyPointType.API_NAME);
        ObjectDataDocument pointsDetail = new ObjectDataDocument();
        pointsDetail.put(IObjectData.NAME, param.getUniqueId());
        pointsDetail.put(LoyaltyConstants.LoyaltyPointsDetail.PROGRAM_ID, pointType.get(LoyaltyConstants.LoyaltyPointType.PROGRAM_ID, String.class));
        pointsDetail.put(LoyaltyConstants.LoyaltyPointsDetail.POINTS_TYPE_ID, pointType.getId());
        pointsDetail.put(LoyaltyConstants.LoyaltyPointsDetail.MEMBER_ID, param.getMemberId());
        pointsDetail.put(LoyaltyConstants.LoyaltyPointsDetail.MEMBER_BEHAVIOR, param.getDesc());
        pointsDetail.put(LoyaltyConstants.LoyaltyPointsDetail.POINTS_QUANTITY, param.getValue());
        pointsDetail.put(LoyaltyConstants.LoyaltyPointsDetail.AVAILABLE_POINTS, param.getValue());
        Long effectiveTime = LoyaltyUtils.formulaEffectiveTime(System.currentTimeMillis(), pointType);
        pointsDetail.put(LoyaltyConstants.LoyaltyPointsDetail.EFFECTIVE_TIME, effectiveTime);
        pointsDetail.put(LoyaltyConstants.LoyaltyPointsDetail.EXPIRY_TIME, LoyaltyUtils.formulaExpiryTime(effectiveTime, pointType));
        if (System.currentTimeMillis() >= effectiveTime) {
            pointsDetail.put(LoyaltyConstants.LoyaltyPointsDetail.POINTS_STATUS, "Available");
        } else {
            pointsDetail.put(LoyaltyConstants.LoyaltyPointsDetail.POINTS_STATUS, "Frozen");
        }
        pointsDetail.put(IObjectData.ID, IdGenerator.get());
        pointsDetail.put(IObjectData.DESCRIBE_API_NAME, LoyaltyConstants.LoyaltyPointsDetail.API_NAME);
        pointsDetail.put(IObjectData.TENANT_ID, tenantId);
        pointsDetail.put(IObjectData.RECORD_TYPE, IObjectData.RECORD_TYPE_DEFAULT);
        serviceFacade.saveObjectData(User.systemUser(tenantId), pointsDetail.toObjectData());
        return pointsDetail;
    }
}
