package com.facishare.crm.sfa.lto.loyalty.service;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.sfa.lto.loyalty.constants.LoyaltyConstants;
import com.facishare.crm.sfa.lto.loyalty.i18n.LoyaltyI18nException;
import com.facishare.crm.sfa.lto.loyalty.model.Loyalty;
import com.facishare.crm.sfa.lto.loyalty.service.memberOperate.ILoyaltyPointsOperateService;
import com.facishare.crm.sfa.lto.loyalty.utils.LoyaltyI18nKey;
import com.facishare.crm.sfa.lto.utils.SearchUtil;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.GetI18nKeyUtil;
import com.fxiaoke.common.StopWatch;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class LoyaltyPointsService {

    @Resource
    private ServiceFacade serviceFacade;
    @Resource
    private List<ILoyaltyPointsOperateService> handlerServiceList;
    @Resource
    private LoyaltyMemberService loyaltyMemberService;
    @Resource
    private LoyaltyPointsOperationCacheService loyaltyPointsOperationCacheService;

    /**
     * 会员操作核心方法
     * 积分增加、减少、转移
     * 等级设定
     */
    public String operate(Loyalty.PointsOperationParam param) {
        String tenantId = param.getTenantId();
        String uniqueId = param.getUniqueId();
        operateCheck(param);
        log.info("会员操作入参{}", JSON.toJSONString(param));
        ILoyaltyPointsOperateService handler = getHandler(param.getType());
        StopWatch stopWatch = StopWatch.createStarted("会员操作开始");
        Set<String> lockMemberIds = handler.lockMemberIds(param);
        RLock rLock = loyaltyMemberService.tryLockMultipleMember(tenantId, lockMemberIds);
        try {
            checkMemberStatus(tenantId, lockMemberIds);
            if (!hasMemberChangeRecord(tenantId, uniqueId)) {
                stopWatch.lap("查询是否已执行");
                handler.action(param);
                stopWatch.lap("执行业务完成");
            }
        } finally {
            loyaltyMemberService.unLockMultipleMember(rLock);
        }
        stopWatch.logSlow(500);
        return uniqueId;
    }

    /**
     * 上述核心方法对应的回退逻辑
     */
    @Transactional
    public String fallback(Loyalty.FallbackParam param) {
        String tenantId = param.getTenantId();
        String memberId = param.getMemberId();
        String uniqueId = param.getUniqueId();
        List<IObjectData> recordList = findRecordList(param);
        //从记录中解析出后续要用到的积分明细Id,用于下文的查询
        Set<String> pointsDetailIds = new HashSet<>();
        //获取锁定的会员Id
        Set<String> lockMemberIds = Sets.newHashSet(memberId);
        Map<String, ILoyaltyPointsOperateService> recordHandlerMap = mapHandlerAndFillRelatedData(recordList, lockMemberIds, pointsDetailIds);
        RLock rLock = loyaltyMemberService.tryLockMultipleMember(tenantId, lockMemberIds);
        try {
            checkMemberStatus(tenantId, lockMemberIds);
            Map<String, IObjectData> pointsDetailMap = findDataMap(tenantId, LoyaltyConstants.LoyaltyPointsDetail.API_NAME, pointsDetailIds);
            Loyalty.FallbackUpdateData updateData = new Loyalty.FallbackUpdateData();
            for (IObjectData record : recordList) {
                if (Boolean.TRUE.equals(record.get(LoyaltyConstants.LoyaltyMemberChangeRecords.IS_FALLBACK, Boolean.class))) {
                    continue;
                }
                ILoyaltyPointsOperateService handler = recordHandlerMap.get(record.getId());
                if (handler != null) {
                    Loyalty.FallbackInfo fallbackInfo = new Loyalty.FallbackInfo();
                    fallbackInfo.setRecord(record);
                    fallbackInfo.setPointsDetail(pointsDetailMap.get(record.get(LoyaltyConstants.LoyaltyMemberChangeRecords.MEMBER_POINTS_DETAIL_ID, String.class)));
                    fallbackInfo.setTransferIntoPointsDetail(pointsDetailMap.get(record.get(LoyaltyConstants.LoyaltyMemberChangeRecords.TRANSFER_INTO_POINTS_DETAIL, String.class)));
                    fallbackInfo.setTransferOutPointsDetail(pointsDetailMap.get(record.get(LoyaltyConstants.LoyaltyMemberChangeRecords.TRANSFER_OUT_POINTS_DETAIL, String.class)));
                    handler.fallback(updateData, fallbackInfo);
                }
            }
            if (!CollectionUtils.isEmpty(updateData.getUpdatePointsDetailList())) {
                serviceFacade.batchUpdate(updateData.getUpdatePointsDetailList(), User.systemUser(tenantId));
            }
            for (String memberIdTemp : lockMemberIds) {
                loyaltyMemberService.unLockUpdateMemberPoints(tenantId, memberIdTemp);
            }
            serviceFacade.batchUpdateByFields(User.systemUser(tenantId), recordList, Lists.newArrayList(LoyaltyConstants.LoyaltyMemberChangeRecords.IS_FALLBACK));
        } finally {
            loyaltyMemberService.unLockMultipleMember(rLock);
        }
        return uniqueId;
    }

    /**
     * 根据记录列表,获取锁定的会员Id和积分明细Id
     */
    public Map<String, ILoyaltyPointsOperateService> mapHandlerAndFillRelatedData(List<IObjectData> recordList, Set<String> lockMemberIds, Set<String> pointsDetailIds) {
        Map<String, ILoyaltyPointsOperateService> recordHandlerMap = new HashMap<>();
        for (IObjectData record : recordList) {
            pointsDetailIds.add(record.get(LoyaltyConstants.LoyaltyMemberChangeRecords.MEMBER_POINTS_DETAIL_ID, String.class));
            String changeType = record.get(LoyaltyConstants.LoyaltyMemberChangeRecords.LOY_CHANGE_TYPE, String.class);
            ILoyaltyPointsOperateService handler = getHandler(Loyalty.PointsOperationParam.Type.valueOf(changeType));
            recordHandlerMap.put(record.getId(), handler);
            if (Loyalty.PointsOperationParam.Type.TRANSFER_CONSUMER_POINTS.toString().equals(changeType)
                    || Loyalty.PointsOperationParam.Type.TRANSFER_LEVEL_POINTS.toString().equals(changeType)
                    || Loyalty.PointsOperationParam.Type.TRANSFER_FREEZE_CONSUMER_POINTS.toString().equals(changeType)
                    || Loyalty.PointsOperationParam.Type.TRANSFER_FREEZE_LEVEL_POINTS.toString().equals(changeType)) {
                String transferIntoMemberId = record.get(LoyaltyConstants.LoyaltyMemberChangeRecords.TRANSFER_INTO_MEMBER, String.class);
                String transferOutMemberId = record.get(LoyaltyConstants.LoyaltyMemberChangeRecords.TRANSFER_OUT_MEMBER, String.class);
                if (!StringUtils.isEmpty(transferIntoMemberId)) {
                    lockMemberIds.add(transferIntoMemberId);
                }
                if (!StringUtils.isEmpty(transferOutMemberId)) {
                    lockMemberIds.add(transferOutMemberId);
                }
                String transferIntoPointDetailId = record.get(LoyaltyConstants.LoyaltyMemberChangeRecords.TRANSFER_INTO_POINTS_DETAIL, String.class);
                String transferOutPointDetailId = record.get(LoyaltyConstants.LoyaltyMemberChangeRecords.TRANSFER_OUT_POINTS_DETAIL, String.class);
                if (!StringUtils.isEmpty(transferIntoPointDetailId)) {
                    pointsDetailIds.add(transferIntoPointDetailId);
                }
                if (!StringUtils.isEmpty(transferOutPointDetailId)) {
                    pointsDetailIds.add(transferOutPointDetailId);
                }
            }
        }
        return recordHandlerMap;
    }

    /**
     * 查询需要回退的记录
     */
    public List<IObjectData> findRecordList(Loyalty.FallbackParam param) {
        String tenantId = param.getTenantId();
        String memberId = param.getMemberId();
        String uniqueId = param.getUniqueId();
        SearchTemplateQuery query = new SearchTemplateQuery();
        List<IFilter> filters = Lists.newArrayList();
        if (StringUtils.isEmpty(uniqueId) && CollectionUtils.isEmpty(param.getDataIdList())) {
            throw LoyaltyI18nException.buildMissingParametersException("uniqueId");
        }
        if (!StringUtils.isEmpty(uniqueId)) {
            SearchUtil.fillFilterEq(filters, IObjectData.NAME, uniqueId);
        }
        if (!CollectionUtils.isEmpty(param.getDataIdList())) {
            SearchUtil.fillFilterIn(filters, IObjectData.ID, param.getDataIdList());
        }
        SearchUtil.fillFilterEq(filters, LoyaltyConstants.LoyaltyMemberChangeRecords.MEMBER_ID, memberId);
        SearchUtil.fillFilterIn(filters, LoyaltyConstants.LoyaltyMemberChangeRecords.LOY_CHANGE_TYPE,
                Arrays.stream(Loyalty.PointsOperationParam.Type.values()).map(Enum::toString).collect(Collectors.toList()));
        query.setFilters(filters);
        QueryResult<IObjectData> recordQueryResult = serviceFacade.findBySearchQuery(User.systemUser(tenantId), LoyaltyConstants.LoyaltyMemberChangeRecords.API_NAME, query);
        if (recordQueryResult == null || CollectionUtils.isEmpty(recordQueryResult.getData())) {
            throw LoyaltyI18nException.build(LoyaltyI18nKey.NOT_FOUND, LoyaltyConstants.LoyaltyMemberChangeRecords.API_NAME);
        }
        return recordQueryResult.getData();
    }

    /**
     * 前置校验
     * 进行参数检查
     */
    public void operateCheck(Loyalty.PointsOperationParam param) {
        String tenantId = param.getTenantId();
        String memberId = param.getMemberId();
        String uniqueId = param.getUniqueId();
        if (StringUtils.isEmpty(tenantId)) {
            throw LoyaltyI18nException.buildMissingParametersException("tenantId");
        }
        if (StringUtils.isEmpty(uniqueId)) {
            throw LoyaltyI18nException.buildMissingParametersException("uniqueId");
        }
        if (StringUtils.isEmpty(memberId)) {
            throw LoyaltyI18nException.buildMissingParametersException("memberId");
        }
        IObjectData program = loyaltyPointsOperationCacheService.getProgram(param);
        if (!"enable".equals(program.get(LoyaltyConstants.LoyaltyProgram.ENABLE_STATUS, String.class))) {
            throw LoyaltyI18nException.build(LoyaltyI18nKey.ErrorStatus.ProgramNotEnable.getErrorCode(), LoyaltyI18nKey.NOT_ENABLED, LoyaltyConstants.LoyaltyProgram.API_NAME);
        }
        param.setProgramId(program.getId());
    }

    public ILoyaltyPointsOperateService getHandler(Loyalty.PointsOperationParam.Type type) {
        for (ILoyaltyPointsOperateService handler : handlerServiceList) {
            if (Objects.equals(handler.type(), type)) {
                return handler;
            }
        }
        throw LoyaltyI18nException.buildInvalidParametersException(LoyaltyConstants.LoyaltyMemberChangeRecords.API_NAME, LoyaltyConstants.LoyaltyMemberChangeRecords.LOY_CHANGE_TYPE);
    }

    /**
     * 查询是否存在记录
     */
    public boolean hasMemberChangeRecord(String tenantId, String uniqueId) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, IObjectData.NAME, uniqueId);
        query.setFilters(filters);
        QueryResult<IObjectData> record = serviceFacade.findBySearchQuery(User.systemUser(tenantId), LoyaltyConstants.LoyaltyMemberChangeRecords.API_NAME, query);
        return record != null && !CollectionUtils.isEmpty(record.getData());
    }


    public Map<String, IObjectData> findDataMap(String tenantId, String objApiName, Collection<String> ids) {
        Map<String, IObjectData> map = new HashMap<>();
        List<IObjectData> dataList = serviceFacade.findObjectDataByIds(tenantId, new ArrayList<>(ids), objApiName);
        if (CollectionUtils.isEmpty(dataList)) {
            return map;
        }
        for (IObjectData data : dataList) {
            map.put(data.getId(), data);
        }
        return map;
    }

    /**
     * 判断会员是否正在合并等不能进行操作的状态
     */
    public void checkMemberStatus(String tenantId, Collection<String> memberIds) {
        List<IObjectData> dataList = serviceFacade.findSimpleDataByIds(tenantId, LoyaltyConstants.LoyaltyMember.API_NAME,
                new ArrayList<>(memberIds),
                Lists.newArrayList(LoyaltyConstants.LoyaltyMember.MEMBER_STATUS),
                ActionContextExt.of(User.systemUser(tenantId)).skipRelevantTeam().getContext()
        );
        for (IObjectData member : dataList) {
            String memberStatus = member.get(LoyaltyConstants.LoyaltyMember.MEMBER_STATUS, String.class);
            if ("merging".equals(memberStatus)) {
                String i18nKey = GetI18nKeyUtil.getOptionNameKey(LoyaltyConstants.LoyaltyMember.API_NAME, LoyaltyConstants.LoyaltyMember.MEMBER_STATUS, memberStatus);
                LoyaltyI18nException.I18nParam param = new LoyaltyI18nException.I18nParam(i18nKey, LoyaltyI18nException.ParamType.variable);
                throw LoyaltyI18nException.build(LoyaltyI18nKey.PROHIBIT_OPERATION_FOR_MEMBER_STATUS, Lists.newArrayList(param));
            }
        }
    }
}
