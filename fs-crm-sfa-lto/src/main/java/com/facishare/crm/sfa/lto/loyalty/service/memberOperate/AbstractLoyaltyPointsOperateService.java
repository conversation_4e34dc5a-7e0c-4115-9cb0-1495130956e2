package com.facishare.crm.sfa.lto.loyalty.service.memberOperate;

import com.facishare.crm.sfa.lto.loyalty.constants.LoyaltyConstants;
import com.facishare.crm.sfa.lto.loyalty.i18n.LoyaltyI18nException;
import com.facishare.crm.sfa.lto.loyalty.model.Loyalty;
import com.facishare.crm.sfa.lto.loyalty.service.LoyaltyMemberChangeRecordsService;
import com.facishare.crm.sfa.lto.loyalty.service.LoyaltyPointsDetailService;
import com.facishare.crm.sfa.lto.loyalty.utils.LoyaltyI18nKey;
import com.facishare.crm.sfa.lto.loyalty.utils.LoyaltyThreshold;
import com.facishare.crm.sfa.lto.loyalty.utils.NumberUtils;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.dao.pg.mapper.metadata.SpecialTableMapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

public abstract class AbstractLoyaltyPointsOperateService implements ILoyaltyPointsOperateService {

    @Resource
    SpecialTableMapper specialTableMapper;
    @Resource
    ServiceFacade serviceFacade;
    @Resource
    LoyaltyPointsDetailService loyaltyPointsDetailService;
    @Resource
    LoyaltyMemberChangeRecordsService loyaltyMemberChangeRecordsService;

    @Override
    public Set<String> lockMemberIds(Loyalty.PointsOperationParam param) {
        return Sets.newHashSet(param.getMemberId());
    }

    /**
     * 积分池积分 扣减
     */
    void decreasePool(String tenantId, String poolId, Long value, String fieldName) {
        String sql = "update biz_loyalty_point_pool set %s = %s - %s where tenant_id = '%s' and id = '%s' and %s >= %s";
        sql = String.format(sql, fieldName, fieldName, value, tenantId, poolId, fieldName, value);
        int count = specialTableMapper.setTenantId(tenantId).batchUpdateBySql(sql);
        if (count < 1) {
            throw LoyaltyI18nException.build(LoyaltyI18nKey.INSUFFICIENT_POINTS, LoyaltyConstants.LoyaltyPointPool.API_NAME);
        }
    }

    /**
     * 积分池积分 加分
     */
    void increasePool(String tenantId, String poolId, Long value, String fieldName) {
        String sql = "update biz_loyalty_point_pool set %s = %s + %s where tenant_id = '%s' and id = '%s'";
        sql = String.format(sql, fieldName, fieldName, value, tenantId, poolId);
        int count = specialTableMapper.setTenantId(tenantId).batchUpdateBySql(sql);
        if (count < 1) {
            throw LoyaltyI18nException.build(LoyaltyI18nKey.NOT_FOUND, LoyaltyConstants.LoyaltyPointPool.API_NAME);
        }
    }

    /**
     * 保存会员信息变动记录
     */
    void saveMemberChangeRecord(Loyalty.PointsOperationParam param, List<ObjectDataDocument> recordDataDocumentList) {
        loyaltyMemberChangeRecordsService.saveMemberChangeRecord(param, recordDataDocumentList);
    }

    /**
     * 更新积分明细(扣减积分)
     */
    List<ObjectDataDocument> updateLoyaltyPointsDetail(String tenantId, long requiredPoints, String poolId, List<IObjectData> pointsDetaislList) {
        List<IObjectData> updatePointsDetaislList = new ArrayList<>();
        List<ObjectDataDocument> insertRecordlList = new ArrayList<>();
        //逐条更新扣减
        for (IObjectData pointsDetail : pointsDetaislList) {
            if (requiredPoints <= 0) {
                break;
            }
            long pointsBefore = NumberUtils.getLong(pointsDetail, LoyaltyConstants.LoyaltyPointsDetail.AVAILABLE_POINTS);
            long pointsAfter;
            if (pointsBefore < requiredPoints) {
                pointsAfter = 0L;
                requiredPoints = requiredPoints - pointsBefore;
            } else {
                pointsAfter = pointsBefore - requiredPoints;
                requiredPoints = 0L;
            }
            if (pointsAfter == 0) {
                pointsDetail.set(LoyaltyConstants.LoyaltyPointsDetail.POINTS_STATUS, "Exhausted");
            }
            pointsDetail.set(LoyaltyConstants.LoyaltyPointsDetail.AVAILABLE_POINTS, pointsAfter);
            updatePointsDetaislList.add(pointsDetail);
            ObjectDataDocument recordDataDocument = new ObjectDataDocument();
            recordDataDocument.put(LoyaltyConstants.LoyaltyMemberChangeRecords.MEMBER_POINTS_DETAIL_ID, pointsDetail.getId());
            recordDataDocument.put(LoyaltyConstants.LoyaltyMemberChangeRecords.POINT_POOL_ID, poolId);
            recordDataDocument.put(LoyaltyConstants.LoyaltyMemberChangeRecords.LOY_CHANGE_VALUE, pointsBefore - pointsAfter);
            recordDataDocument.put(LoyaltyConstants.LoyaltyMemberChangeRecords.LOY_CHANGE_TYPE, type().toString());
            insertRecordlList.add(recordDataDocument);
        }
        if (requiredPoints > 0) {
            throw LoyaltyI18nException.build(LoyaltyI18nKey.ErrorStatus.MemberPointsInsufficient.getErrorCode(),
                    LoyaltyI18nKey.INSUFFICIENT_POINTS_OR_EXCEEDS_LIMIT,
                    Lists.newArrayList(new LoyaltyI18nException.I18nParam(String.valueOf(LoyaltyThreshold.getPointsDetailsLimit()))));
        }
        serviceFacade.batchUpdateByFields(User.systemUser(tenantId), updatePointsDetaislList,
                Lists.newArrayList(LoyaltyConstants.LoyaltyPointsDetail.AVAILABLE_POINTS, LoyaltyConstants.LoyaltyPointsDetail.POINTS_STATUS));
        return insertRecordlList;
    }
}
