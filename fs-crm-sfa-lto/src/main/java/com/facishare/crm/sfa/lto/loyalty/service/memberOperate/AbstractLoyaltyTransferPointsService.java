package com.facishare.crm.sfa.lto.loyalty.service.memberOperate;

import com.facishare.crm.sfa.lto.loyalty.constants.LoyaltyConstants;
import com.facishare.crm.sfa.lto.loyalty.i18n.LoyaltyI18nException;
import com.facishare.crm.sfa.lto.loyalty.model.Loyalty;
import com.facishare.crm.sfa.lto.loyalty.service.LoyaltyMemberService;
import com.facishare.crm.sfa.lto.loyalty.service.memberTierStrategy.IMemberTierStrategy;
import com.facishare.crm.sfa.lto.loyalty.utils.LoyaltyI18nKey;
import com.facishare.crm.sfa.lto.loyalty.utils.LoyaltyThreshold;
import com.facishare.crm.sfa.lto.loyalty.utils.NumberUtils;
import com.facishare.crm.sfa.lto.utils.SearchUtil;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.fxiaoke.api.IdGenerator;
import com.fxiaoke.common.StopWatch;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

public abstract class AbstractLoyaltyTransferPointsService extends AbstractLoyaltyPointsOperateService {

    @Resource
    LoyaltyMemberService loyaltyMemberService;

    final static List<String> updatePointDetailsFields = Lists.newArrayList(LoyaltyConstants.LoyaltyPointsDetail.AVAILABLE_POINTS
            , LoyaltyConstants.LoyaltyPointsDetail.POINTS_STATUS
    );

    @Override
    public Set<String> lockMemberIds(Loyalty.PointsOperationParam param) {
        if (StringUtils.isEmpty(param.getTransferMemberId())) {
            throw LoyaltyI18nException.buildMissingParametersException(LoyaltyConstants.LoyaltyMemberChangeRecords.API_NAME,
                    LoyaltyConstants.LoyaltyMemberChangeRecords.TRANSFER_INTO_MEMBER);
        }
        return Sets.newHashSet(param.getMemberId(), param.getTransferMemberId());
    }

    /**
     * 获取不同行为的过滤条件
     */
    abstract List<IFilter> findMemberPointsDetailsFilter();

    abstract String counterpointType();

    @Transactional
    @Override
    public void action(Loyalty.PointsOperationParam param) {
        String tenantId = param.getTenantId();
        StopWatch stopWatch = StopWatch.createStarted("会员积分转移");
        //根据积分类型,获取扣除积分的会员积分明细
        List<IFilter> filters = findMemberPointsDetailsFilter();
        IFilter iFilter = SearchUtil.filter(LoyaltyConstants.LoyaltyPointsDetail.ALLOW_GIFTING, Operator.EQ, true);
        iFilter.setIsMasterField(true);
        filters.add(iFilter);
        List<IObjectData> outPointsDetaislList = loyaltyPointsDetailService.findMemberPointsDetails(param.getTenantId(), param.getMemberId(), filters);
        stopWatch.lap("查询可扣减的积分明细");
        //新的积分明细
        List<IObjectData> intoPointDetailsList = Lists.newArrayList();
        //会员变动记录
        List<ObjectDataDocument> insertRecordlList = new ArrayList<>();
        //需要扣除的积分
        long requiredPointsValue = param.getValue();
        //获取参数
        String outMemberId = param.getMemberId();
        String intoMemberId = param.getTransferMemberId();
        //扣除旧会员积分明细
        List<IObjectData> updateOutPointsDetaislList = new ArrayList<>();
        for (IObjectData outPointDetails : outPointsDetaislList) {
            if (requiredPointsValue <= 0) {
                break;
            }
            //生成转入积分明细
            IObjectData intoPointDetails = buildIntoPointDetails(outPointDetails, param);
            //积分扣减
            long pointsBefore = NumberUtils.getLong(outPointDetails, LoyaltyConstants.LoyaltyPointsDetail.AVAILABLE_POINTS);
            long changePointsValue;
            if (pointsBefore <= requiredPointsValue) {
                changePointsValue = pointsBefore;
                requiredPointsValue = requiredPointsValue - changePointsValue;
                //用完前是可用的
                if ("Available".equals(outPointDetails.get(LoyaltyConstants.LoyaltyPointsDetail.POINTS_STATUS, String.class))) {
                    outPointDetails.set(LoyaltyConstants.LoyaltyPointsDetail.POINTS_STATUS, "Exhausted");
                }
            } else {
                changePointsValue = requiredPointsValue;
                requiredPointsValue = 0L;
            }
            NumberUtils.sum(outPointDetails, LoyaltyConstants.LoyaltyPointsDetail.AVAILABLE_POINTS, -changePointsValue);
            NumberUtils.sum(intoPointDetails, LoyaltyConstants.LoyaltyPointsDetail.POINTS_QUANTITY, changePointsValue);
            NumberUtils.sum(intoPointDetails, LoyaltyConstants.LoyaltyPointsDetail.AVAILABLE_POINTS, changePointsValue);
            intoPointDetailsList.add(intoPointDetails);
            updateOutPointsDetaislList.add(outPointDetails);
            insertRecordlList.addAll(buildAllRecord(outPointDetails, intoPointDetails, changePointsValue));
        }
        stopWatch.lap("积分扣减计算");
        if (requiredPointsValue > 0) {
            throw LoyaltyI18nException.build(LoyaltyI18nKey.ErrorStatus.MemberPointsInsufficient.getErrorCode(),
                    LoyaltyI18nKey.INSUFFICIENT_POINTS_OR_EXCEEDS_LIMIT,
                    Lists.newArrayList(new LoyaltyI18nException.I18nParam(String.valueOf(LoyaltyThreshold.getPointsDetailsLimit()))));
        }
        serviceFacade.batchUpdateByFields(User.systemUser(tenantId), updateOutPointsDetaislList, updatePointDetailsFields);
        stopWatch.lap("更新旧积分明细");
        serviceFacade.bulkSaveObjectData(intoPointDetailsList, User.systemUser(tenantId));
        stopWatch.lap("插入新积分明细");
        saveMemberChangeRecord(param, insertRecordlList);
        stopWatch.lap("插入操作记录");
        loyaltyMemberService.unLockUpdateMemberPoints(tenantId, outMemberId);
        if (Loyalty.PointsOperationParam.Type.TRANSFER_LEVEL_POINTS.equals(type())) {
            loyaltyMemberService.unLockUpdateMemberPointsAndTier(tenantId, intoMemberId, IMemberTierStrategy.Operator.upgrades);
        } else {
            loyaltyMemberService.unLockUpdateMemberPoints(tenantId, intoMemberId);
        }
        stopWatch.lap("会员重算积分");
        stopWatch.logSlow(STOP_WATCH_TIME);
    }

    @Override
    public void fallback(Loyalty.FallbackUpdateData updateData, Loyalty.FallbackInfo fallbackInfo) {
        IObjectData record = fallbackInfo.getRecord();
        //现有初始化只有单积分明细，需要补充初始化两个积分明细
        IObjectData transferIntoPointsDetail = fallbackInfo.getTransferIntoPointsDetail();
        IObjectData transferOutPointsDetail = fallbackInfo.getTransferOutPointsDetail();
        if (transferIntoPointsDetail == null || transferOutPointsDetail == null) {
            throw LoyaltyI18nException.build(LoyaltyI18nKey.NOT_FOUND, LoyaltyConstants.LoyaltyPointsDetail.API_NAME);
        }
        //根据记录进行积分反转操作,积分不足返回失败,需要更新积分状态:可用/作废
        long changePointsValue = NumberUtils.getLong(record, LoyaltyConstants.LoyaltyMemberChangeRecords.LOY_CHANGE_VALUE);
        if (NumberUtils.notEnoughToReduce(transferIntoPointsDetail, LoyaltyConstants.LoyaltyPointsDetail.AVAILABLE_POINTS, changePointsValue)) {
            throw LoyaltyI18nException.build(LoyaltyI18nKey.INSUFFICIENT_POINTS, LoyaltyConstants.LoyaltyPointsDetail.API_NAME);
        }
        NumberUtils.sum(transferIntoPointsDetail, LoyaltyConstants.LoyaltyPointsDetail.AVAILABLE_POINTS, -changePointsValue);
        if (NumberUtils.getLong(transferIntoPointsDetail, LoyaltyConstants.LoyaltyPointsDetail.AVAILABLE_POINTS) == 0) {//积分已用完
            if ("Available".equals(transferIntoPointsDetail.get(LoyaltyConstants.LoyaltyPointsDetail.POINTS_STATUS, String.class))) {//用完前是可用的
                transferIntoPointsDetail.set(LoyaltyConstants.LoyaltyPointsDetail.POINTS_STATUS, "Exhausted");
            }
        }
        NumberUtils.sum(transferOutPointsDetail, LoyaltyConstants.LoyaltyPointsDetail.AVAILABLE_POINTS, changePointsValue);
        if ("Exhausted".equals(transferOutPointsDetail.get(LoyaltyConstants.LoyaltyPointsDetail.POINTS_STATUS, String.class))) {
            transferOutPointsDetail.set(LoyaltyConstants.LoyaltyPointsDetail.POINTS_STATUS, "Available");
        }
        //积分明细放入更新列表
        updateData.getUpdatePointsDetailList().add(transferIntoPointsDetail);
        updateData.getUpdatePointsDetailList().add(transferOutPointsDetail);
        //设置回退状态-是
        record.set(LoyaltyConstants.LoyaltyMemberChangeRecords.IS_FALLBACK, true);
    }

    public List<ObjectDataDocument> buildAllRecord(IObjectData outPointDetails, IObjectData intoPointDetails, Long value) {
        List<ObjectDataDocument> recordDataDocumentList = Lists.newArrayList();
        String outMemberId = outPointDetails.get(LoyaltyConstants.LoyaltyPointsDetail.MEMBER_ID, String.class);
        String intoMemberId = intoPointDetails.get(LoyaltyConstants.LoyaltyPointsDetail.MEMBER_ID, String.class);
        String outPointsDetailId = outPointDetails.getId();
        String intoPointsDetailId = intoPointDetails.getId();
        ObjectDataDocument outRecordDataDocument = buildRecord(outPointDetails, intoPointDetails, value, type().toString());
        outRecordDataDocument.put(LoyaltyConstants.LoyaltyMemberChangeRecords.MEMBER_ID, outMemberId);
        outRecordDataDocument.put(LoyaltyConstants.LoyaltyMemberChangeRecords.MEMBER_POINTS_DETAIL_ID, outPointsDetailId);
        recordDataDocumentList.add(outRecordDataDocument);
        ObjectDataDocument intoRecordDataDocument = buildRecord(outPointDetails, intoPointDetails, value, counterpointType());
        intoRecordDataDocument.put(LoyaltyConstants.LoyaltyMemberChangeRecords.MEMBER_ID, intoMemberId);
        intoRecordDataDocument.put(LoyaltyConstants.LoyaltyMemberChangeRecords.MEMBER_POINTS_DETAIL_ID, intoPointsDetailId);
        recordDataDocumentList.add(intoRecordDataDocument);
        return recordDataDocumentList;
    }

    public ObjectDataDocument buildRecord(IObjectData outPointDetails, IObjectData intoPointDetails, Long value, String type) {
        String outMemberId = outPointDetails.get(LoyaltyConstants.LoyaltyPointsDetail.MEMBER_ID, String.class);
        String intoMemberId = intoPointDetails.get(LoyaltyConstants.LoyaltyPointsDetail.MEMBER_ID, String.class);
        String outPointsDetailId = outPointDetails.getId();
        String intoPointsDetailId = intoPointDetails.getId();
        ObjectDataDocument recordDataDocument = new ObjectDataDocument();
        recordDataDocument.put(LoyaltyConstants.LoyaltyMemberChangeRecords.TRANSFER_OUT_MEMBER, outMemberId);
        recordDataDocument.put(LoyaltyConstants.LoyaltyMemberChangeRecords.TRANSFER_INTO_MEMBER, intoMemberId);
        recordDataDocument.put(LoyaltyConstants.LoyaltyMemberChangeRecords.TRANSFER_OUT_POINTS_DETAIL, outPointsDetailId);
        recordDataDocument.put(LoyaltyConstants.LoyaltyMemberChangeRecords.TRANSFER_INTO_POINTS_DETAIL, intoPointsDetailId);
        recordDataDocument.put(LoyaltyConstants.LoyaltyMemberChangeRecords.LOY_CHANGE_VALUE, value);
        recordDataDocument.put(LoyaltyConstants.LoyaltyMemberChangeRecords.LOY_CHANGE_TYPE, type);
        return recordDataDocument;
    }

    public IObjectData buildIntoPointDetails(IObjectData outPointDetails, Loyalty.PointsOperationParam param) {
        IObjectData intoPointDetails = ObjectDataExt.of(outPointDetails).copy();
        intoPointDetails.set(IObjectData.ID, IdGenerator.get());
        intoPointDetails.set(LoyaltyConstants.LoyaltyPointsDetail.MEMBER_ID, param.getTransferMemberId());
        intoPointDetails.setName(param.getUniqueId());
        intoPointDetails.set(LoyaltyConstants.LoyaltyPointsDetail.POINTS_QUANTITY, 0);
        intoPointDetails.set(LoyaltyConstants.LoyaltyPointsDetail.AVAILABLE_POINTS, 0);
        return intoPointDetails;
    }
}
