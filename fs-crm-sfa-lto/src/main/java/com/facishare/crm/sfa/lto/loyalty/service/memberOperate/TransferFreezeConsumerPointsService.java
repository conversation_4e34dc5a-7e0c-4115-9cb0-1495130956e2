package com.facishare.crm.sfa.lto.loyalty.service.memberOperate;

import com.facishare.crm.sfa.lto.loyalty.constants.LoyaltyConstants;
import com.facishare.crm.sfa.lto.loyalty.model.Loyalty;
import com.facishare.crm.sfa.lto.utils.SearchUtil;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class TransferFreezeConsumerPointsService extends AbstractLoyaltyTransferPointsService {
    @Override
    public Loyalty.PointsOperationParam.Type type() {
        return Loyalty.PointsOperationParam.Type.TRANSFER_FREEZE_CONSUMER_POINTS;
    }

    @Override
    List<IFilter> findMemberPointsDetailsFilter() {
        List<IFilter> filterList = Lists.newArrayList();
        IFilter iFilter = SearchUtil.filter(LoyaltyConstants.LoyaltyPointsDetail.IS_QUALIFYING, Operator.EQ, false);
        iFilter.setIsMasterField(true);
        filterList.add(iFilter);
        SearchUtil.fillFilterEq(filterList, LoyaltyConstants.LoyaltyPointsDetail.POINTS_STATUS, "Frozen");
        return filterList;
    }

    @Override
    String counterpointType() {
        return "ADD_FREEZE_CONSUMER_POINTS_BY_MEMBER";
    }
}
