package com.facishare.crm.sfa.lto.loyalty.service.memberTierStrategy;

import com.facishare.crm.sfa.lto.loyalty.constants.LoyaltyConstants;
import com.facishare.crm.sfa.lto.loyalty.service.LoyaltyMemberChangeRecordsService;
import com.facishare.crm.sfa.lto.loyalty.service.LoyaltyPointsDetailService;
import com.facishare.crm.sfa.lto.loyalty.utils.LoyaltyUtils;
import com.facishare.crm.sfa.lto.loyalty.utils.NumberUtils;
import com.facishare.crm.sfa.lto.loyalty.utils.ServiceFacadeUtils;
import com.facishare.crm.sfa.lto.utils.SearchUtil;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;

@Slf4j
@Service
public abstract class AbstractMemberTierStrategy implements IMemberTierStrategy {

    @Resource
    ServiceFacade serviceFacade;
    @Resource
    LoyaltyPointsDetailService loyaltyPointsDetailService;
    @Resource
    LoyaltyMemberChangeRecordsService loyaltyMemberChangeRecordsService;

    @Override
    public Map<String, Object> upgrades(String tenantId, Param param) {
        Map<String, Object> updateFields = new HashMap<>();
        IObjectData member = param.getMember();
        IObjectData tierInfo = param.getCurrentTier();
        IObjectData tierClassInfo = param.getTierClassInfo();
        long points = NumberUtils.getLong(member, LoyaltyConstants.LoyaltyMember.GRADING_POINTS);
        if (param.isNotChangeTierClass()) {//未发生等级分类变更
            //是否重新计算
            if (points <= NumberUtils.getLong(tierInfo, LoyaltyConstants.LoyaltyTier.POINTS_TO_NEXT_TIER)) {
                //当前积分不超过当前等级所需积分，不进行升级
                return updateFields;
            }
        }
        IObjectData afterTier = getAccessibleMaxTierForUpgrades(tenantId, tierClassInfo.getId(), points);
        if (afterTier != null && !Objects.equals(tierInfo.getId(), afterTier.getId())) {
            updateFields.put(LoyaltyConstants.LoyaltyMember.TIER_ID, afterTier.getId());
            updateFields.put(LoyaltyConstants.LoyaltyMember.TIER_START_TIME, System.currentTimeMillis());
            loyaltyMemberChangeRecordsService.saveMemberChangeRecord(tenantId, member, tierInfo.getId(), afterTier.getId());
        }
        return updateFields;
    }

    @Override
    public Map<String, Object> task(String tenantId, Param param) {
        Map<String, Object> updateFields = new HashMap<>();
        IObjectData member = param.getMember();
        IObjectData tierInfo = param.getCurrentTier();
        IObjectData tierClassInfo = param.getTierClassInfo();
        //上次评定日
        long lastEvaluationDate = NumberUtils.getLong(member, LoyaltyConstants.LoyaltyMember.EVALUATION_DATE);
        //更新下次评定日
        updateFields.put(LoyaltyConstants.LoyaltyMember.EVALUATION_DATE, LoyaltyUtils.evaluationDate(tierClassInfo, member));
        if (ltMinimumRatingTime(member, tierClassInfo)) {
            log.info("当前等级不满足升级最短时间要求，不进行评定.会员id{}", member.getId());
            return updateFields;
        }
        //获取清空前的积分，用于计算等级
        long points = NumberUtils.getLong(member, LoyaltyConstants.LoyaltyMember.GRADING_POINTS);
        if (Boolean.TRUE.equals(tierClassInfo.get(LoyaltyConstants.LoyaltyTierClass.CLEAR_POINTS, Boolean.class))) {
            clearPoints(tenantId, member, lastEvaluationDate);
            updateFields.putAll(loyaltyPointsDetailService.fillPoints(tenantId, member.getId()));
        }
        if (param.isNotChangeTierClass()) {//未发生等级分类变更
            //是否重新计算
            if (points >= NumberUtils.getLong(tierInfo, LoyaltyConstants.LoyaltyTier.POINTS_REQUIRED)) {
                //当前积分超过当前等级最低积分,进行保级,不进行重算
                return updateFields;
            }
        }
        IObjectData afterTier = getAccessibleMaxTier(tenantId, tierClassInfo.getId(), points);
        if (afterTier != null) {
            updateFields.put(LoyaltyConstants.LoyaltyMember.TIER_ID, afterTier.getId());
            updateFields.put(LoyaltyConstants.LoyaltyMember.TIER_START_TIME, System.currentTimeMillis());
            loyaltyMemberChangeRecordsService.saveMemberChangeRecord(tenantId, member, tierInfo.getId(), afterTier.getId());
        }
        return updateFields;
    }

    /**
     * 清空评定日之前的定级积分
     */
    public void clearPoints(String tenantId, IObjectData member, long evaluationDate) {
        ServiceFacadeUtils.foreach(query -> {
                    List<IFilter> filters = query.getFilters();
                    SearchUtil.fillFilterEq(filters, IObjectData.IS_DELETED, 0);
                    SearchUtil.fillFilterEq(filters, LoyaltyConstants.LoyaltyPointsDetail.MEMBER_ID, member.getId());
                    IFilter iFilter = SearchUtil.filter(LoyaltyConstants.LoyaltyPointsDetail.IS_QUALIFYING, com.facishare.paas.metadata.impl.search.Operator.EQ, true);
                    iFilter.setIsMasterField(true);
                    filters.add(iFilter);
                    SearchUtil.fillFilterLT(filters, IObjectData.CREATE_TIME, evaluationDate);
                    SearchUtil.fillFilterNotEq(filters, LoyaltyConstants.LoyaltyPointsDetail.POINTS_STATUS, "Clear");
                    query.setFilters(filters);
                    QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQuery(User.systemUser(tenantId), LoyaltyConstants.LoyaltyPointsDetail.API_NAME, query);
                    if (queryResult != null && !CollectionUtils.isEmpty(queryResult.getData())) {
                        List<IObjectData> updateList = new ArrayList<>();
                        for (IObjectData pointsDetail : queryResult.getData()) {
                            pointsDetail.set(LoyaltyConstants.LoyaltyPointsDetail.POINTS_STATUS, "Clear");
                            updateList.add(pointsDetail);
                        }
                        serviceFacade.batchUpdateByFields(User.systemUser(tenantId), updateList, Lists.newArrayList(LoyaltyConstants.LoyaltyPointsDetail.POINTS_STATUS));
                    }
                    return queryResult;
                }
        );
    }

    /**
     * 查询指定等级分类下，所需积分与升级所需积分在所属区间的，升级所需积分最小的等级
     */
    IObjectData getAccessibleMaxTierForUpgrades(String tenantId, String tierClassId, Long points) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, LoyaltyConstants.LoyaltyTier.TIER_STATUS, "Valid");
        SearchUtil.fillFilterEq(filters, LoyaltyConstants.LoyaltyTier.TIER_CLASS_ID, tierClassId);
        SearchUtil.fillFilterLTE(filters, LoyaltyConstants.LoyaltyTier.POINTS_REQUIRED, points);
        SearchUtil.fillFilterGTE(filters, LoyaltyConstants.LoyaltyTier.POINTS_TO_NEXT_TIER, points);
        query.setFilters(filters);
        query.setLimit(1);
        query.setOrders(Lists.newArrayList(new OrderBy(LoyaltyConstants.LoyaltyTier.POINTS_TO_NEXT_TIER, true)));
        QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQuery(User.systemUser(tenantId), LoyaltyConstants.LoyaltyTier.API_NAME, query);
        if (queryResult == null || CollectionUtils.isEmpty(queryResult.getData())) {
            return null;
        }
        return queryResult.getData().get(0);
    }

    /**
     * 根据指定的等级分类下，获取当前积分可达的最高等级
     */
    IObjectData getAccessibleMaxTier(String tenantId, String tierClassId, Long points) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, LoyaltyConstants.LoyaltyTier.TIER_STATUS, "Valid");
        SearchUtil.fillFilterEq(filters, LoyaltyConstants.LoyaltyTier.TIER_CLASS_ID, tierClassId);
        SearchUtil.fillFilterLTE(filters, LoyaltyConstants.LoyaltyTier.POINTS_REQUIRED, points);
        query.setFilters(filters);
        query.setLimit(1);
        query.setOrders(Lists.newArrayList(new OrderBy(LoyaltyConstants.LoyaltyTier.POINTS_REQUIRED, false)));
        QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQuery(User.systemUser(tenantId), LoyaltyConstants.LoyaltyTier.API_NAME, query);
        if (queryResult == null || CollectionUtils.isEmpty(queryResult.getData())) {
            return null;
        }
        return queryResult.getData().get(0);
    }

    /**
     * 小于最小等级维持时间，不进行评级
     */
    boolean ltMinimumRatingTime(IObjectData member, IObjectData tierClassInfo) {
        Integer validityPeriodLength = tierClassInfo.get(LoyaltyConstants.LoyaltyTierClass.MINIMUM_LEVEL_DURATION, Integer.class);
        if (validityPeriodLength == null) {
            validityPeriodLength = 0;
        }
        String unit = tierClassInfo.get(LoyaltyConstants.LoyaltyTierClass.MINIMUM_LEVEL_DURATION_UNIT, String.class);
        long startTime = NumberUtils.getLong(member, LoyaltyConstants.LoyaltyMember.TIER_START_TIME);
        Calendar afterSpecifiedTime = Calendar.getInstance();
        afterSpecifiedTime.setTimeInMillis(startTime);
        if ("Yearly".equals(unit)) {
            afterSpecifiedTime.add(Calendar.YEAR, validityPeriodLength);
        } else if ("Quarterly".equals(unit)) {
            afterSpecifiedTime.add(Calendar.MONTH, 3 * validityPeriodLength);
        } else if ("Monthly".equals(unit)) {
            afterSpecifiedTime.add(Calendar.MONTH, validityPeriodLength);
        }
        return System.currentTimeMillis() < afterSpecifiedTime.getTimeInMillis();
    }
}
