package com.facishare.crm.sfa.lto.loyalty.utils;

import com.github.autoconf.ConfigFactory;
import lombok.Getter;

public class LoyaltyThreshold {

    /**
     * 限制积分明细一次性使用上线
     */
    @Getter
    private static Integer pointsDetailsLimit;

    /**
     * 积分明细最大有效期
     * 选择时效性为不限制的情况下，设定该值
     */
    @Getter
    private static final Long pointDetailsMaximumValidPeriod = 253402271999999L;

    /**
     * 通用分页限制条数
     */
    @Getter
    private static Integer commonPageSize;

    /**
     * 通用全量查询上限
     * 例如：查询全量会员计划，估测不会超过 commonMaxSize
     */
    @Getter
    private static Integer commonMaxSize;

    /**
     * 强制更新下次评定日的天数
     * 例如：15天内 规则无更新，不进行下次评定日计算
     */
    @Getter
    private static Integer evaluationDateCalculationPeriod;


    static {
        ConfigFactory.getConfig("fs-crm-sales-config", config -> {
            pointsDetailsLimit = config.getInt("loyalty_pointsDetailsLimit", 100);
            commonPageSize = config.getInt("loyalty_commonPageSize", 100);
            commonMaxSize = config.getInt("loyalty_commonMaxSize", 1000);
            evaluationDateCalculationPeriod = config.getInt("loyalty_evaluationDateCalculationPeriod", 15);
        });
    }

}
