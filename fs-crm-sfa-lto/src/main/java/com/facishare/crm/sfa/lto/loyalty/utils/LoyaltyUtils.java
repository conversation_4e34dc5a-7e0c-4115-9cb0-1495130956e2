package com.facishare.crm.sfa.lto.loyalty.utils;

import com.facishare.crm.sfa.lto.loyalty.constants.LoyaltyConstants;
import com.facishare.paas.metadata.api.IObjectData;

import java.util.Calendar;
import java.util.function.Consumer;

public class LoyaltyUtils {

    public static long evaluationDate(IObjectData tierClassInfo, IObjectData member) {
        int evaluationMonth, evaluationDay;
        String levelMode = tierClassInfo.get(LoyaltyConstants.LoyaltyTierClass.LEVEL_MODE, String.class);
        if ("FixedDateModel".equals(levelMode)) {
            evaluationDay = NumberUtils.getInt(tierClassInfo, LoyaltyConstants.LoyaltyTierClass.LEVEL_START_DAY);
            String assessmentFrequency = tierClassInfo.get(LoyaltyConstants.LoyaltyTierClass.ASSESSMENT_FREQUENCY, String.class);
            if ("Annual".equals(assessmentFrequency)) {//年度才需要输入月份,其他从1开始
                //代码中月份从0开始，页面输入月份需要-1
                evaluationMonth = NumberUtils.getInt(tierClassInfo, LoyaltyConstants.LoyaltyTierClass.LEVEL_START_MONTH) - 1;
            } else {
                evaluationMonth = 0;
            }
        } else {
            Calendar calendar = Calendar.getInstance();
            calendar.setTimeInMillis(NumberUtils.getLong(member, LoyaltyConstants.LoyaltyMember.REGISTRATION_TIME));
            evaluationMonth = calendar.get(Calendar.MONTH);
            evaluationDay = calendar.get(Calendar.DAY_OF_MONTH);
        }
        return evaluationDate(tierClassInfo, evaluationMonth, evaluationDay);
    }

    private static long evaluationDate(IObjectData tierClassInfo, int month, int day) {
        Calendar target = Calendar.getInstance();
        target.set(Calendar.MONTH, month);
        target.set(Calendar.DAY_OF_MONTH, 1);
        target.set(Calendar.HOUR_OF_DAY, 23);
        target.set(Calendar.MINUTE, 59);
        target.set(Calendar.SECOND, 59);
        target.set(Calendar.MILLISECOND, 999);

        String assessmentFrequency = tierClassInfo.get(LoyaltyConstants.LoyaltyTierClass.ASSESSMENT_FREQUENCY, String.class);
        if ("Annual".equals(assessmentFrequency)) {
            target.set(Calendar.MONTH, month);
            addByAssessmentFrequency(target, day, (t) -> t.add(Calendar.YEAR, 1));
        } else if ("Quarterly".equals(assessmentFrequency)) {
            addByAssessmentFrequency(target, day, (t) -> t.add(Calendar.MONTH, 3));
        } else {
            addByAssessmentFrequency(target, day, (t) -> t.add(Calendar.MONTH, 1));
        }
        return target.getTimeInMillis();
    }

    public static void addByAssessmentFrequency(Calendar target, int day, Consumer<Calendar> calendarConsumer) {
        long currentTime = System.currentTimeMillis();
        target.set(Calendar.DAY_OF_MONTH, Math.min(day, target.getActualMaximum(Calendar.DAY_OF_MONTH)));
        while (currentTime >= target.getTimeInMillis()) {
            calendarConsumer.accept(target);
            target.set(Calendar.DAY_OF_MONTH, Math.min(day, target.getActualMaximum(Calendar.DAY_OF_MONTH)));
        }
    }

    public static Long formulaEffectiveTime(Long creatTime, IObjectData pointType) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(creatTime);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        switch (pointType.get(LoyaltyConstants.LoyaltyPointType.ACTIVATION_METHOD, String.class)) {
            case "Immediately": {
                calendar.setTimeInMillis(creatTime);
                break;
            }
            case "NextWeek": {
                int week = calendar.get(Calendar.DAY_OF_WEEK);
                week = week == 1 ? 8 : week;
                calendar.add(Calendar.DAY_OF_YEAR, 9 - week);
                break;
            }
            case "NextMonth": {
                calendar.add(Calendar.MONTH, 1);
                calendar.set(Calendar.DAY_OF_MONTH, 1);
                break;
            }
            case "NextQuarter": {
                calendar.add(Calendar.MONTH, 3);
                int month = calendar.get((Calendar.MONTH));
                if (month <= 2) {
                    calendar.set(Calendar.MONTH, 0);
                } else if (month <= 5) {
                    calendar.set(Calendar.MONTH, 3);
                } else if (month <= 8) {
                    calendar.set(Calendar.MONTH, 6);
                } else {
                    calendar.set(Calendar.MONTH, 9);
                }
                calendar.set(Calendar.DAY_OF_MONTH, 1);
                break;
            }
            case "NextYear": {
                calendar.add(Calendar.YEAR, 1);
                calendar.set(Calendar.DAY_OF_YEAR, 1);
                break;
            }
            case "AfterFreezePeriod": {
                Integer freeze = pointType.get(LoyaltyConstants.LoyaltyPointType.FREEZE_PERIOD_DAYS, Integer.class);
                if (freeze == null) {
                    freeze = 0;
                }
                calendar.add(Calendar.DAY_OF_MONTH, freeze);
                break;
            }
        }
        return calendar.getTimeInMillis();
    }

    public static Long formulaExpiryTime(Long effectiveTime, IObjectData pointType) {
        if ("FixedDate".equals(pointType.get(LoyaltyConstants.LoyaltyPointType.EXPIRY_METHOD, String.class))) {
            return pointType.get(LoyaltyConstants.LoyaltyPointType.EXPIRY_DATE, Long.class);
        }
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(effectiveTime);
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 0);
        Integer validityPeriodLength = pointType.get(LoyaltyConstants.LoyaltyPointType.VALIDITY_PERIOD_LENGTH, Integer.class);
        if (validityPeriodLength == null) {
            validityPeriodLength = 0;
        }
        String validityPeriodUnit = pointType.get(LoyaltyConstants.LoyaltyPointType.VALIDITY_PERIOD_UNIT, String.class);
        switch (validityPeriodUnit) {
            case "Day": {
                calendar.add(Calendar.DAY_OF_MONTH, validityPeriodLength);
                break;
            }
            case "Week": {
                calendar.add(Calendar.DAY_OF_MONTH, 7 * validityPeriodLength);
                break;
            }
            case "Month": {
                calendar.add(Calendar.MONTH, validityPeriodLength);
                break;
            }
            case "Quarter": {
                calendar.add(Calendar.MONTH, 3 * validityPeriodLength);
                break;
            }
            case "Year": {
                calendar.add(Calendar.YEAR, validityPeriodLength);
                break;
            }
            case "Unlimited": {//无限制设定为固定时间
                calendar.setTimeInMillis(LoyaltyThreshold.getPointDetailsMaximumValidPeriod());
                break;
            }
        }
        return calendar.getTimeInMillis();
    }
}
