package com.facishare.crm.sfa.lto.loyalty.utils;

import com.facishare.crm.sfa.lto.utils.SearchUtil;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.function.Function;

public class ServiceFacadeUtils {

    private static final Logger log = LoggerFactory.getLogger(ServiceFacadeUtils.class);

    /**
     * 通过id偏移遍历
     */
    public static void foreach(Function<SearchTemplateQuery, QueryResult<IObjectData>> searchFunc) {
        String lastDataId = null;
        for (int limit = LoyaltyThreshold.getCommonPageSize(), i = 0; true; i++) {
            SearchTemplateQuery query = new SearchTemplateQuery();
            List<IFilter> filters = query.getFilters();
            if (!StringUtils.isEmpty(lastDataId)) {
                SearchUtil.fillFilterGT(filters, IObjectData.ID, lastDataId);
            }
            query.setFilters(filters);
            query.setLimit(limit);
            query.setOrders(Lists.newArrayList(new OrderBy(IObjectData.ID, true)));
            QueryResult<IObjectData> queryResult = searchFunc.apply(query);
            if (queryResult == null || CollectionUtils.isEmpty(queryResult.getData())) {
                break;
            }
            List<IObjectData> dataList = queryResult.getData();
            IObjectData lastData = dataList.get(dataList.size() - 1);
            lastDataId = lastData.getId();
            if (dataList.size() < limit) {
                break;
            }
            if (i >= 999999) {
                log.warn("循环超长[{}],可能出现死循环", i);
            }
        }
    }

}
